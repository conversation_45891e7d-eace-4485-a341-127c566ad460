[28-Jul-2025 14:46:14 Europe/Berlin] [TEST CONNECTION INIT] Parámetros recibidos - TestPC: LOCALHOST | CodeEmp: 1 | CodSuc: 33
[28-Jul-2025 14:46:14 Europe/Berlin] [TEST CONNECTION SPECIFIC] TestPC encontrado en configuración | Servidor: ************** | Base de datos: SUC33 | Usuario: SA | Empresa: zavidoro
[28-Jul-2025 14:46:14 Europe/Berlin] [TEST CONNECTION] Tipo: Prueba Específica (TESTPC:LOCALHOST, CODEMP:1, CODSUC:33) | Servidor: ************** | Base de datos: SUC33 | Usuario: SA | Timestamp: 2025-07-28 14:46:14
[28-Jul-2025 14:46:30 Europe/Berlin] [TEST CONNECTION ERROR] Prueba Específica (TESTPC:LOCALHOST, CODEMP:1, CODSUC:33) | Servidor intentado: ************** | Base de datos: SUC33 | Error: SQLSTATE: 08001, Code: 2, Message: [Microsoft][ODBC Driver 17 for SQL Server]Named Pipes Provider: Could not open a connection to SQL Server [2]. ; SQLSTATE: HYT00, Code: 0, Message: [Microsoft][ODBC Driver 17 for SQL Server]Login timeout expired; SQLSTATE: 08001, Code: 2, Message: [Microsoft][ODBC Driver 17 for SQL Server]A network-related or instance-specific error has occurred while establishing a connection to SQL Server. Server is not found or not accessible. Check if instance name is correct and if SQL Server is configured to allow remote connections. For more information see SQL Server Books Online.
[28-Jul-2025 14:46:30 Europe/Berlin] Falló la conexión Prueba Específica (TESTPC:LOCALHOST, CODEMP:1, CODSUC:33): SQLSTATE: 08001, Code: 2, Message: [Microsoft][ODBC Driver 17 for SQL Server]Named Pipes Provider: Could not open a connection to SQL Server [2]. ; SQLSTATE: HYT00, Code: 0, Message: [Microsoft][ODBC Driver 17 for SQL Server]Login timeout expired; SQLSTATE: 08001, Code: 2, Message: [Microsoft][ODBC Driver 17 for SQL Server]A network-related or instance-specific error has occurred while establishing a connection to SQL Server. Server is not found or not accessible. Check if instance name is correct and if SQL Server is configured to allow remote connections. For more information see SQL Server Books Online.
[28-Jul-2025 14:47:58 Europe/Berlin] [TEST CONNECTION INIT] Parámetros recibidos - TestPC: LOCALHOST | CodeEmp: 1 | CodSuc: 33
[28-Jul-2025 14:47:58 Europe/Berlin] [TEST CONNECTION SPECIFIC] TestPC encontrado en configuración | Servidor: ************** | Base de datos: SUC33 | Usuario: SA | Empresa: zavidoro
[28-Jul-2025 14:47:58 Europe/Berlin] [TEST CONNECTION] Tipo: Prueba Específica (TESTPC:LOCALHOST, CODEMP:1, CODSUC:33) | Servidor: ************** | Base de datos: SUC33 | Usuario: SA | Timestamp: 2025-07-28 14:47:58
[28-Jul-2025 14:47:58 Europe/Berlin] [TEST CONNECTION SUCCESS] Prueba Específica (TESTPC:LOCALHOST, CODEMP:1, CODSUC:33) | Servidor: ************** | Base de datos: SUC33 | Conexión establecida exitosamente
[28-Jul-2025 14:49:32 Europe/Berlin] [TEST CONNECTION INIT] Parámetros recibidos - TestPC: LOCALHOST | CodeEmp: 1 | CodSuc: 33
[28-Jul-2025 14:49:32 Europe/Berlin] [TEST CONNECTION SPECIFIC] TestPC encontrado en configuración | Servidor: ************** | Base de datos: SUC33 | Usuario: SA | Empresa: zavidoro
[28-Jul-2025 14:49:32 Europe/Berlin] [TEST CONNECTION] Tipo: Prueba Específica (TESTPC:LOCALHOST, CODEMP:1, CODSUC:33) | Servidor: ************** | Base de datos: SUC33 | Usuario: SA | Timestamp: 2025-07-28 14:49:32
[28-Jul-2025 14:49:32 Europe/Berlin] [TEST CONNECTION SUCCESS] Prueba Específica (TESTPC:LOCALHOST, CODEMP:1, CODSUC:33) | Servidor: ************** | Base de datos: SUC33 | Conexión establecida exitosamente
[28-Jul-2025 14:49:44 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[28-Jul-2025 14:49:44 Europe/Berlin] consultarEstado.php - Content-Type: application/json
[28-Jul-2025 14:49:44 Europe/Berlin] consultarEstado.php - Query String: 
[28-Jul-2025 14:49:44 Europe/Berlin] consultarEstado.php - GET params: []
[28-Jul-2025 14:49:44 Europe/Berlin] consultarEstado.php - POST params: []
[28-Jul-2025 14:49:44 Europe/Berlin] consultarEstado.php - Input raw length: 30
[28-Jul-2025 14:49:44 Europe/Berlin] consultarEstado.php - Input raw: '{
	"hookalias": "SQRGV14065"
}'
[28-Jul-2025 14:49:44 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SQRGV14065"}
[28-Jul-2025 14:49:44 Europe/Berlin] consultarEstado.php - JSON last error: No error
[28-Jul-2025 14:49:44 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SQRGV14065
[28-Jul-2025 14:49:44 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/bancard/consultarEstado.php con datos: {"hookalias":"SQRGV14065"}
[28-Jul-2025 14:49:44 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[28-Jul-2025 14:49:44 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[{"id":38,"hook_alias":"SQRGV14065","status":"confirmed","url":null,"fecha_creacion":{"date":"2025-07-20 22:38:25.893000","timezone_type":3,"timezone":"UTC"},"fecha_actualizacion":{"date":"2025-07-20 22:38:25.893000","timezone_type":3,"timezone":"UTC"},"response_code":"00","response_description":"Pago Exitoso","amount":"39950.00","currency":"GS","installment_number":1,"description":"Compra en Tienda  - Monto: 39950 - Fecha: 20\/07\/2025 22:38:13","date_time":{"date":"2025-07-20 22:38:25.893000","timezone_type":3,"timezone":"UTC"},"ticket_number":"**********","authorization_code":"872203","commerce_name":"ZAVIDORO CORPORATIONS SUC.PY.","branch_name":"NIKE-SOL","bin":"************","merchant_code":"553149","payer_name":null,"payer_lastname":null,"card_last_numbers":2045,"account_type":"TC","created_at":null}]}
[28-Jul-2025 14:49:51 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[28-Jul-2025 14:49:51 Europe/Berlin] consultarEstado.php - Content-Type: application/json
[28-Jul-2025 14:49:51 Europe/Berlin] consultarEstado.php - Query String: 
[28-Jul-2025 14:49:51 Europe/Berlin] consultarEstado.php - GET params: []
[28-Jul-2025 14:49:51 Europe/Berlin] consultarEstado.php - POST params: []
[28-Jul-2025 14:49:51 Europe/Berlin] consultarEstado.php - Input raw length: 30
[28-Jul-2025 14:49:51 Europe/Berlin] consultarEstado.php - Input raw: '{
	"hookalias": "SSQOF91580"
}'
[28-Jul-2025 14:49:51 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SSQOF91580"}
[28-Jul-2025 14:49:51 Europe/Berlin] consultarEstado.php - JSON last error: No error
[28-Jul-2025 14:49:51 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SSQOF91580
[28-Jul-2025 14:49:51 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/bancard/consultarEstado.php con datos: {"hookalias":"SSQOF91580"}
[28-Jul-2025 14:49:51 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[28-Jul-2025 14:49:51 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[{"id":56,"hook_alias":"SSQOF91580","status":"confirmed","url":null,"fecha_creacion":{"date":"2025-07-21 13:41:06.520000","timezone_type":3,"timezone":"UTC"},"fecha_actualizacion":{"date":"2025-07-21 13:41:06.520000","timezone_type":3,"timezone":"UTC"},"response_code":"00","response_description":"Pago exitoso","amount":"20000.00","currency":"GS","installment_number":10,"description":"Coca Cola 3 Ltrs.","date_time":{"date":"2025-07-21 13:41:06.520000","timezone_type":3,"timezone":"UTC"},"ticket_number":"**************","authorization_code":"134243","commerce_name":"Super Acme","branch_name":"Sucursal 001","bin":"433234","merchant_code":"51111","payer_name":null,"payer_lastname":null,"card_last_numbers":1234,"account_type":"TC","created_at":null}]}
[29-Jul-2025 18:34:04 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[29-Jul-2025 18:34:04 Europe/Berlin] consultarEstado.php - Content-Type: application/json
[29-Jul-2025 18:34:04 Europe/Berlin] consultarEstado.php - Query String: 
[29-Jul-2025 18:34:04 Europe/Berlin] consultarEstado.php - GET params: []
[29-Jul-2025 18:34:04 Europe/Berlin] consultarEstado.php - POST params: []
[29-Jul-2025 18:34:04 Europe/Berlin] consultarEstado.php - Input raw length: 30
[29-Jul-2025 18:34:04 Europe/Berlin] consultarEstado.php - Input raw: '{
	"hookalias": "SSQOF91580"
}'
[29-Jul-2025 18:34:04 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SSQOF91580"}
[29-Jul-2025 18:34:04 Europe/Berlin] consultarEstado.php - JSON last error: No error
[29-Jul-2025 18:34:04 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SSQOF91580
[29-Jul-2025 18:34:04 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/bancard/consultarEstado.php con datos: {"hookalias":"SSQOF91580"}
[29-Jul-2025 18:34:05 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[29-Jul-2025 18:34:05 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[{"id":56,"hook_alias":"SSQOF91580","status":"confirmed","url":null,"fecha_creacion":{"date":"2025-07-21 13:41:06.520000","timezone_type":3,"timezone":"UTC"},"fecha_actualizacion":{"date":"2025-07-21 13:41:06.520000","timezone_type":3,"timezone":"UTC"},"response_code":"00","response_description":"Pago exitoso","amount":"20000.00","currency":"GS","installment_number":10,"description":"Coca Cola 3 Ltrs.","date_time":{"date":"2025-07-21 13:41:06.520000","timezone_type":3,"timezone":"UTC"},"ticket_number":"**************","authorization_code":"134243","commerce_name":"Super Acme","branch_name":"Sucursal 001","bin":"433234","merchant_code":"51111","payer_name":null,"payer_lastname":null,"card_last_numbers":1234,"account_type":"TC","created_at":null}]}
[29-Jul-2025 20:25:37 Europe/Berlin] registrarAlias.php - Método HTTP: PUT
[29-Jul-2025 20:25:37 Europe/Berlin] registrarAlias.php - Content-Type: application/json; charset=utf-8
[29-Jul-2025 20:25:37 Europe/Berlin] registrarAlias.php - Query String: 
[29-Jul-2025 20:25:37 Europe/Berlin] registrarAlias.php - GET params: []
[29-Jul-2025 20:25:37 Europe/Berlin] registrarAlias.php - POST params: []
[29-Jul-2025 20:25:37 Europe/Berlin] registrarAlias.php - Input raw length: 120
[29-Jul-2025 20:25:37 Europe/Berlin] registrarAlias.php - Input raw: '{"hookalias":"SOWYC39502","amount":185280,"description":"Compra en Tienda de Nike","created_at":"29\/07\/2025 15:25:36"}'
[29-Jul-2025 20:25:37 Europe/Berlin] registrarAlias.php - JSON decode result: {"hookalias":"SOWYC39502","amount":185280,"description":"Compra en Tienda de Nike","created_at":"29\/07\/2025 15:25:36"}
[29-Jul-2025 20:25:37 Europe/Berlin] registrarAlias.php - JSON last error: No error
[29-Jul-2025 20:25:37 Europe/Berlin] registrarAlias.php - hookalias encontrado en JSON body: SOWYC39502
[29-Jul-2025 20:25:37 Europe/Berlin] registrarAlias.php - amount encontrado en JSON body: 185280
[29-Jul-2025 20:25:37 Europe/Berlin] registrarAlias.php - description encontrado en JSON body: Compra en Tienda de Nike
[29-Jul-2025 20:25:37 Europe/Berlin] registrarAlias.php - created_at encontrado en JSON body: 29/07/2025 15:25:36
[29-Jul-2025 20:25:37 Europe/Berlin] registrarAlias.php - Enviando a: https://zavidoro.com.py/bancard/registrarAlias.php con datos: {"hookalias":"SOWYC39502","amount":185280,"description":"Compra en Tienda de Nike","created_at":"29\/07\/2025 15:25:36"}
[29-Jul-2025 20:25:38 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[29-Jul-2025 20:25:38 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[29-Jul-2025 20:25:38 Europe/Berlin] consultarEstado.php - Query String: 
[29-Jul-2025 20:25:38 Europe/Berlin] consultarEstado.php - GET params: []
[29-Jul-2025 20:25:38 Europe/Berlin] consultarEstado.php - POST params: []
[29-Jul-2025 20:25:38 Europe/Berlin] consultarEstado.php - Input raw length: 26
[29-Jul-2025 20:25:38 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SOWYC39502"}'
[29-Jul-2025 20:25:38 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SOWYC39502"}
[29-Jul-2025 20:25:38 Europe/Berlin] consultarEstado.php - JSON last error: No error
[29-Jul-2025 20:25:38 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SOWYC39502
[29-Jul-2025 20:25:38 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/bancard/consultarEstado.php con datos: {"hookalias":"SOWYC39502"}
[29-Jul-2025 20:25:38 Europe/Berlin] registrarAlias.php - Respuesta del servidor remoto - HTTP Code: 200
[29-Jul-2025 20:25:38 Europe/Berlin] registrarAlias.php - Respuesta del servidor remoto - Body: {"status":"success","message":"Alias registrado correctamente"}
[29-Jul-2025 20:25:38 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[29-Jul-2025 20:25:38 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[{"id":59,"hook_alias":"SOWYC39502","status":"pendiente","url":null,"fecha_creacion":{"date":"2025-07-29 15:25:38.603000","timezone_type":3,"timezone":"UTC"},"fecha_actualizacion":null,"response_code":null,"response_description":null,"amount":null,"currency":null,"installment_number":null,"description":"Compra en Tienda de Nike","date_time":null,"ticket_number":null,"authorization_code":null,"commerce_name":null,"branch_name":null,"bin":null,"merchant_code":null,"payer_name":null,"payer_lastname":null,"card_last_numbers":null,"account_type":null,"created_at":null}]}
[29-Jul-2025 20:25:43 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[29-Jul-2025 20:25:43 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[29-Jul-2025 20:25:43 Europe/Berlin] consultarEstado.php - Query String: 
[29-Jul-2025 20:25:43 Europe/Berlin] consultarEstado.php - GET params: []
[29-Jul-2025 20:25:43 Europe/Berlin] consultarEstado.php - POST params: []
[29-Jul-2025 20:25:43 Europe/Berlin] consultarEstado.php - Input raw length: 26
[29-Jul-2025 20:25:43 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SOWYC39502"}'
[29-Jul-2025 20:25:43 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SOWYC39502"}
[29-Jul-2025 20:25:43 Europe/Berlin] consultarEstado.php - JSON last error: No error
[29-Jul-2025 20:25:43 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SOWYC39502
[29-Jul-2025 20:25:43 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/bancard/consultarEstado.php con datos: {"hookalias":"SOWYC39502"}
[29-Jul-2025 20:25:43 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[29-Jul-2025 20:25:43 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[{"id":59,"hook_alias":"SOWYC39502","status":"pendiente","url":null,"fecha_creacion":{"date":"2025-07-29 15:25:38.603000","timezone_type":3,"timezone":"UTC"},"fecha_actualizacion":null,"response_code":null,"response_description":null,"amount":null,"currency":null,"installment_number":null,"description":"Compra en Tienda de Nike","date_time":null,"ticket_number":null,"authorization_code":null,"commerce_name":null,"branch_name":null,"bin":null,"merchant_code":null,"payer_name":null,"payer_lastname":null,"card_last_numbers":null,"account_type":null,"created_at":null}]}
[29-Jul-2025 20:25:48 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[29-Jul-2025 20:25:48 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[29-Jul-2025 20:25:48 Europe/Berlin] consultarEstado.php - Query String: 
[29-Jul-2025 20:25:48 Europe/Berlin] consultarEstado.php - GET params: []
[29-Jul-2025 20:25:48 Europe/Berlin] consultarEstado.php - POST params: []
[29-Jul-2025 20:25:48 Europe/Berlin] consultarEstado.php - Input raw length: 26
[29-Jul-2025 20:25:48 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SOWYC39502"}'
[29-Jul-2025 20:25:48 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SOWYC39502"}
[29-Jul-2025 20:25:48 Europe/Berlin] consultarEstado.php - JSON last error: No error
[29-Jul-2025 20:25:48 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SOWYC39502
[29-Jul-2025 20:25:48 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/bancard/consultarEstado.php con datos: {"hookalias":"SOWYC39502"}
[29-Jul-2025 20:25:48 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[29-Jul-2025 20:25:48 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[{"id":59,"hook_alias":"SOWYC39502","status":"pendiente","url":null,"fecha_creacion":{"date":"2025-07-29 15:25:38.603000","timezone_type":3,"timezone":"UTC"},"fecha_actualizacion":null,"response_code":null,"response_description":null,"amount":null,"currency":null,"installment_number":null,"description":"Compra en Tienda de Nike","date_time":null,"ticket_number":null,"authorization_code":null,"commerce_name":null,"branch_name":null,"bin":null,"merchant_code":null,"payer_name":null,"payer_lastname":null,"card_last_numbers":null,"account_type":null,"created_at":null}]}
[29-Jul-2025 20:25:54 Europe/Berlin] registrarAlias.php - Método HTTP: PUT
[29-Jul-2025 20:25:54 Europe/Berlin] registrarAlias.php - Content-Type: application/json; charset=utf-8
[29-Jul-2025 20:25:54 Europe/Berlin] registrarAlias.php - Query String: 
[29-Jul-2025 20:25:54 Europe/Berlin] registrarAlias.php - GET params: []
[29-Jul-2025 20:25:54 Europe/Berlin] registrarAlias.php - POST params: []
[29-Jul-2025 20:25:54 Europe/Berlin] registrarAlias.php - Input raw length: 120
[29-Jul-2025 20:25:54 Europe/Berlin] registrarAlias.php - Input raw: '{"hookalias":"SGTQO13690","amount":185280,"description":"Compra en Tienda de Nike","created_at":"29\/07\/2025 15:25:53"}'
[29-Jul-2025 20:25:54 Europe/Berlin] registrarAlias.php - JSON decode result: {"hookalias":"SGTQO13690","amount":185280,"description":"Compra en Tienda de Nike","created_at":"29\/07\/2025 15:25:53"}
[29-Jul-2025 20:25:54 Europe/Berlin] registrarAlias.php - JSON last error: No error
[29-Jul-2025 20:25:54 Europe/Berlin] registrarAlias.php - hookalias encontrado en JSON body: SGTQO13690
[29-Jul-2025 20:25:54 Europe/Berlin] registrarAlias.php - amount encontrado en JSON body: 185280
[29-Jul-2025 20:25:54 Europe/Berlin] registrarAlias.php - description encontrado en JSON body: Compra en Tienda de Nike
[29-Jul-2025 20:25:54 Europe/Berlin] registrarAlias.php - created_at encontrado en JSON body: 29/07/2025 15:25:53
[29-Jul-2025 20:25:54 Europe/Berlin] registrarAlias.php - Enviando a: https://zavidoro.com.py/bancard/registrarAlias.php con datos: {"hookalias":"SGTQO13690","amount":185280,"description":"Compra en Tienda de Nike","created_at":"29\/07\/2025 15:25:53"}
[29-Jul-2025 20:25:54 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[29-Jul-2025 20:25:54 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[29-Jul-2025 20:25:54 Europe/Berlin] consultarEstado.php - Query String: 
[29-Jul-2025 20:25:54 Europe/Berlin] consultarEstado.php - GET params: []
[29-Jul-2025 20:25:54 Europe/Berlin] consultarEstado.php - POST params: []
[29-Jul-2025 20:25:54 Europe/Berlin] consultarEstado.php - Input raw length: 26
[29-Jul-2025 20:25:54 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SGTQO13690"}'
[29-Jul-2025 20:25:54 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SGTQO13690"}
[29-Jul-2025 20:25:54 Europe/Berlin] consultarEstado.php - JSON last error: No error
[29-Jul-2025 20:25:54 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SGTQO13690
[29-Jul-2025 20:25:54 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/bancard/consultarEstado.php con datos: {"hookalias":"SGTQO13690"}
[29-Jul-2025 20:25:54 Europe/Berlin] registrarAlias.php - Respuesta del servidor remoto - HTTP Code: 200
[29-Jul-2025 20:25:54 Europe/Berlin] registrarAlias.php - Respuesta del servidor remoto - Body: {"status":"success","message":"Alias registrado correctamente"}
[29-Jul-2025 20:25:55 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[29-Jul-2025 20:25:55 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[{"id":60,"hook_alias":"SGTQO13690","status":"pendiente","url":null,"fecha_creacion":{"date":"2025-07-29 15:25:54.920000","timezone_type":3,"timezone":"UTC"},"fecha_actualizacion":null,"response_code":null,"response_description":null,"amount":null,"currency":null,"installment_number":null,"description":"Compra en Tienda de Nike","date_time":null,"ticket_number":null,"authorization_code":null,"commerce_name":null,"branch_name":null,"bin":null,"merchant_code":null,"payer_name":null,"payer_lastname":null,"card_last_numbers":null,"account_type":null,"created_at":null}]}
[29-Jul-2025 20:25:59 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[29-Jul-2025 20:25:59 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[29-Jul-2025 20:25:59 Europe/Berlin] consultarEstado.php - Query String: 
[29-Jul-2025 20:25:59 Europe/Berlin] consultarEstado.php - GET params: []
[29-Jul-2025 20:25:59 Europe/Berlin] consultarEstado.php - POST params: []
[29-Jul-2025 20:25:59 Europe/Berlin] consultarEstado.php - Input raw length: 26
[29-Jul-2025 20:25:59 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SGTQO13690"}'
[29-Jul-2025 20:25:59 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SGTQO13690"}
[29-Jul-2025 20:25:59 Europe/Berlin] consultarEstado.php - JSON last error: No error
[29-Jul-2025 20:25:59 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SGTQO13690
[29-Jul-2025 20:25:59 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/bancard/consultarEstado.php con datos: {"hookalias":"SGTQO13690"}
[29-Jul-2025 20:25:59 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[29-Jul-2025 20:25:59 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[{"id":60,"hook_alias":"SGTQO13690","status":"pendiente","url":null,"fecha_creacion":{"date":"2025-07-29 15:25:54.920000","timezone_type":3,"timezone":"UTC"},"fecha_actualizacion":null,"response_code":null,"response_description":null,"amount":null,"currency":null,"installment_number":null,"description":"Compra en Tienda de Nike","date_time":null,"ticket_number":null,"authorization_code":null,"commerce_name":null,"branch_name":null,"bin":null,"merchant_code":null,"payer_name":null,"payer_lastname":null,"card_last_numbers":null,"account_type":null,"created_at":null}]}
[29-Jul-2025 20:26:04 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[29-Jul-2025 20:26:04 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[29-Jul-2025 20:26:04 Europe/Berlin] consultarEstado.php - Query String: 
[29-Jul-2025 20:26:04 Europe/Berlin] consultarEstado.php - GET params: []
[29-Jul-2025 20:26:04 Europe/Berlin] consultarEstado.php - POST params: []
[29-Jul-2025 20:26:04 Europe/Berlin] consultarEstado.php - Input raw length: 26
[29-Jul-2025 20:26:04 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SGTQO13690"}'
[29-Jul-2025 20:26:04 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SGTQO13690"}
[29-Jul-2025 20:26:04 Europe/Berlin] consultarEstado.php - JSON last error: No error
[29-Jul-2025 20:26:04 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SGTQO13690
[29-Jul-2025 20:26:04 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/bancard/consultarEstado.php con datos: {"hookalias":"SGTQO13690"}
[29-Jul-2025 20:26:04 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[29-Jul-2025 20:26:04 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[{"id":60,"hook_alias":"SGTQO13690","status":"pendiente","url":null,"fecha_creacion":{"date":"2025-07-29 15:25:54.920000","timezone_type":3,"timezone":"UTC"},"fecha_actualizacion":null,"response_code":null,"response_description":null,"amount":null,"currency":null,"installment_number":null,"description":"Compra en Tienda de Nike","date_time":null,"ticket_number":null,"authorization_code":null,"commerce_name":null,"branch_name":null,"bin":null,"merchant_code":null,"payer_name":null,"payer_lastname":null,"card_last_numbers":null,"account_type":null,"created_at":null}]}
[29-Jul-2025 20:26:09 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[29-Jul-2025 20:26:09 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[29-Jul-2025 20:26:09 Europe/Berlin] consultarEstado.php - Query String: 
[29-Jul-2025 20:26:09 Europe/Berlin] consultarEstado.php - GET params: []
[29-Jul-2025 20:26:09 Europe/Berlin] consultarEstado.php - POST params: []
[29-Jul-2025 20:26:09 Europe/Berlin] consultarEstado.php - Input raw length: 26
[29-Jul-2025 20:26:09 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SGTQO13690"}'
[29-Jul-2025 20:26:09 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SGTQO13690"}
[29-Jul-2025 20:26:09 Europe/Berlin] consultarEstado.php - JSON last error: No error
[29-Jul-2025 20:26:09 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SGTQO13690
[29-Jul-2025 20:26:09 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/bancard/consultarEstado.php con datos: {"hookalias":"SGTQO13690"}
[29-Jul-2025 20:26:09 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[29-Jul-2025 20:26:09 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[{"id":60,"hook_alias":"SGTQO13690","status":"pendiente","url":null,"fecha_creacion":{"date":"2025-07-29 15:25:54.920000","timezone_type":3,"timezone":"UTC"},"fecha_actualizacion":null,"response_code":null,"response_description":null,"amount":null,"currency":null,"installment_number":null,"description":"Compra en Tienda de Nike","date_time":null,"ticket_number":null,"authorization_code":null,"commerce_name":null,"branch_name":null,"bin":null,"merchant_code":null,"payer_name":null,"payer_lastname":null,"card_last_numbers":null,"account_type":null,"created_at":null}]}
[29-Jul-2025 22:29:41 Europe/Berlin] [TEST CONNECTION INIT] Parámetros recibidos - TestPC: HOST | CodeEmp: 1 | CodSuc: 33
[29-Jul-2025 22:29:41 Europe/Berlin] [TEST CONNECTION SPECIFIC] TestPC encontrado en configuración | Servidor: ************** | Base de datos: SUC33 | Usuario: SA | Empresa: zavidoro
[29-Jul-2025 22:29:41 Europe/Berlin] [TEST CONNECTION] Tipo: Prueba Específica (TESTPC:HOST, CODEMP:1, CODSUC:33) | Servidor: ************** | Base de datos: SUC33 | Usuario: SA | Timestamp: 2025-07-29 22:29:41
[29-Jul-2025 22:29:41 Europe/Berlin] [TEST CONNECTION SUCCESS] Prueba Específica (TESTPC:HOST, CODEMP:1, CODSUC:33) | Servidor: ************** | Base de datos: SUC33 | Conexión establecida exitosamente
[29-Jul-2025 22:31:33 Europe/Berlin] registrarAlias.php - Método HTTP: PUT
[29-Jul-2025 22:31:34 Europe/Berlin] registrarAlias.php - Content-Type: application/json; charset=utf-8
[29-Jul-2025 22:31:34 Europe/Berlin] registrarAlias.php - Query String: 
[29-Jul-2025 22:31:34 Europe/Berlin] registrarAlias.php - GET params: []
[29-Jul-2025 22:31:34 Europe/Berlin] registrarAlias.php - POST params: []
[29-Jul-2025 22:31:34 Europe/Berlin] registrarAlias.php - Input raw length: 120
[29-Jul-2025 22:31:34 Europe/Berlin] registrarAlias.php - Input raw: '{"hookalias":"SDLQU63285","amount":199530,"description":"Compra en Tienda de Nike","created_at":"29\/07\/2025 17:31:32"}'
[29-Jul-2025 22:31:34 Europe/Berlin] registrarAlias.php - JSON decode result: {"hookalias":"SDLQU63285","amount":199530,"description":"Compra en Tienda de Nike","created_at":"29\/07\/2025 17:31:32"}
[29-Jul-2025 22:31:34 Europe/Berlin] registrarAlias.php - JSON last error: No error
[29-Jul-2025 22:31:34 Europe/Berlin] registrarAlias.php - hookalias encontrado en JSON body: SDLQU63285
[29-Jul-2025 22:31:34 Europe/Berlin] registrarAlias.php - amount encontrado en JSON body: 199530
[29-Jul-2025 22:31:34 Europe/Berlin] registrarAlias.php - description encontrado en JSON body: Compra en Tienda de Nike
[29-Jul-2025 22:31:34 Europe/Berlin] registrarAlias.php - created_at encontrado en JSON body: 29/07/2025 17:31:32
[29-Jul-2025 22:31:34 Europe/Berlin] registrarAlias.php - Enviando a: https://zavidoro.com.py/bancard/registrarAlias.php con datos: {"hookalias":"SDLQU63285","amount":199530,"description":"Compra en Tienda de Nike","created_at":"29\/07\/2025 17:31:32"}
[29-Jul-2025 22:31:34 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[29-Jul-2025 22:31:34 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[29-Jul-2025 22:31:34 Europe/Berlin] consultarEstado.php - Query String: 
[29-Jul-2025 22:31:34 Europe/Berlin] consultarEstado.php - GET params: []
[29-Jul-2025 22:31:34 Europe/Berlin] consultarEstado.php - POST params: []
[29-Jul-2025 22:31:34 Europe/Berlin] consultarEstado.php - Input raw length: 26
[29-Jul-2025 22:31:34 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SDLQU63285"}'
[29-Jul-2025 22:31:34 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SDLQU63285"}
[29-Jul-2025 22:31:34 Europe/Berlin] consultarEstado.php - JSON last error: No error
[29-Jul-2025 22:31:34 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SDLQU63285
[29-Jul-2025 22:31:34 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/bancard/consultarEstado.php con datos: {"hookalias":"SDLQU63285"}
[29-Jul-2025 22:31:34 Europe/Berlin] registrarAlias.php - Respuesta del servidor remoto - HTTP Code: 200
[29-Jul-2025 22:31:34 Europe/Berlin] registrarAlias.php - Respuesta del servidor remoto - Body: {"status":"success","message":"Alias registrado correctamente"}
[29-Jul-2025 22:31:34 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[29-Jul-2025 22:31:34 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[{"id":61,"hook_alias":"SDLQU63285","status":"pendiente","url":null,"fecha_creacion":{"date":"2025-07-29 17:31:34.687000","timezone_type":3,"timezone":"UTC"},"fecha_actualizacion":null,"response_code":null,"response_description":null,"amount":null,"currency":null,"installment_number":null,"description":"Compra en Tienda de Nike","date_time":null,"ticket_number":null,"authorization_code":null,"commerce_name":null,"branch_name":null,"bin":null,"merchant_code":null,"payer_name":null,"payer_lastname":null,"card_last_numbers":null,"account_type":null,"created_at":null}]}
[29-Jul-2025 22:31:39 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[29-Jul-2025 22:31:39 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[29-Jul-2025 22:31:39 Europe/Berlin] consultarEstado.php - Query String: 
[29-Jul-2025 22:31:39 Europe/Berlin] consultarEstado.php - GET params: []
[29-Jul-2025 22:31:39 Europe/Berlin] consultarEstado.php - POST params: []
[29-Jul-2025 22:31:39 Europe/Berlin] consultarEstado.php - Input raw length: 26
[29-Jul-2025 22:31:39 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SDLQU63285"}'
[29-Jul-2025 22:31:39 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SDLQU63285"}
[29-Jul-2025 22:31:39 Europe/Berlin] consultarEstado.php - JSON last error: No error
[29-Jul-2025 22:31:39 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SDLQU63285
[29-Jul-2025 22:31:39 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/bancard/consultarEstado.php con datos: {"hookalias":"SDLQU63285"}
[29-Jul-2025 22:31:39 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[29-Jul-2025 22:31:39 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[{"id":61,"hook_alias":"SDLQU63285","status":"pendiente","url":null,"fecha_creacion":{"date":"2025-07-29 17:31:34.687000","timezone_type":3,"timezone":"UTC"},"fecha_actualizacion":null,"response_code":null,"response_description":null,"amount":null,"currency":null,"installment_number":null,"description":"Compra en Tienda de Nike","date_time":null,"ticket_number":null,"authorization_code":null,"commerce_name":null,"branch_name":null,"bin":null,"merchant_code":null,"payer_name":null,"payer_lastname":null,"card_last_numbers":null,"account_type":null,"created_at":null}]}
[29-Jul-2025 22:31:44 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[29-Jul-2025 22:31:44 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[29-Jul-2025 22:31:44 Europe/Berlin] consultarEstado.php - Query String: 
[29-Jul-2025 22:31:44 Europe/Berlin] consultarEstado.php - GET params: []
[29-Jul-2025 22:31:44 Europe/Berlin] consultarEstado.php - POST params: []
[29-Jul-2025 22:31:44 Europe/Berlin] consultarEstado.php - Input raw length: 26
[29-Jul-2025 22:31:44 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SDLQU63285"}'
[29-Jul-2025 22:31:44 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SDLQU63285"}
[29-Jul-2025 22:31:44 Europe/Berlin] consultarEstado.php - JSON last error: No error
[29-Jul-2025 22:31:44 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SDLQU63285
[29-Jul-2025 22:31:44 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/bancard/consultarEstado.php con datos: {"hookalias":"SDLQU63285"}
[29-Jul-2025 22:31:44 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[29-Jul-2025 22:31:44 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[{"id":61,"hook_alias":"SDLQU63285","status":"pendiente","url":null,"fecha_creacion":{"date":"2025-07-29 17:31:34.687000","timezone_type":3,"timezone":"UTC"},"fecha_actualizacion":null,"response_code":null,"response_description":null,"amount":null,"currency":null,"installment_number":null,"description":"Compra en Tienda de Nike","date_time":null,"ticket_number":null,"authorization_code":null,"commerce_name":null,"branch_name":null,"bin":null,"merchant_code":null,"payer_name":null,"payer_lastname":null,"card_last_numbers":null,"account_type":null,"created_at":null}]}
[29-Jul-2025 22:31:49 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[29-Jul-2025 22:31:49 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[29-Jul-2025 22:31:49 Europe/Berlin] consultarEstado.php - Query String: 
[29-Jul-2025 22:31:49 Europe/Berlin] consultarEstado.php - GET params: []
[29-Jul-2025 22:31:49 Europe/Berlin] consultarEstado.php - POST params: []
[29-Jul-2025 22:31:49 Europe/Berlin] consultarEstado.php - Input raw length: 26
[29-Jul-2025 22:31:49 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SDLQU63285"}'
[29-Jul-2025 22:31:49 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SDLQU63285"}
[29-Jul-2025 22:31:49 Europe/Berlin] consultarEstado.php - JSON last error: No error
[29-Jul-2025 22:31:49 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SDLQU63285
[29-Jul-2025 22:31:49 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/bancard/consultarEstado.php con datos: {"hookalias":"SDLQU63285"}
[29-Jul-2025 22:31:49 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[29-Jul-2025 22:31:49 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[{"id":61,"hook_alias":"SDLQU63285","status":"pendiente","url":null,"fecha_creacion":{"date":"2025-07-29 17:31:34.687000","timezone_type":3,"timezone":"UTC"},"fecha_actualizacion":null,"response_code":null,"response_description":null,"amount":null,"currency":null,"installment_number":null,"description":"Compra en Tienda de Nike","date_time":null,"ticket_number":null,"authorization_code":null,"commerce_name":null,"branch_name":null,"bin":null,"merchant_code":null,"payer_name":null,"payer_lastname":null,"card_last_numbers":null,"account_type":null,"created_at":null}]}
[29-Jul-2025 22:31:54 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[29-Jul-2025 22:31:54 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[29-Jul-2025 22:31:54 Europe/Berlin] consultarEstado.php - Query String: 
[29-Jul-2025 22:31:54 Europe/Berlin] consultarEstado.php - GET params: []
[29-Jul-2025 22:31:54 Europe/Berlin] consultarEstado.php - POST params: []
[29-Jul-2025 22:31:54 Europe/Berlin] consultarEstado.php - Input raw length: 26
[29-Jul-2025 22:31:54 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SDLQU63285"}'
[29-Jul-2025 22:31:54 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SDLQU63285"}
[29-Jul-2025 22:31:54 Europe/Berlin] consultarEstado.php - JSON last error: No error
[29-Jul-2025 22:31:54 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SDLQU63285
[29-Jul-2025 22:31:54 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/bancard/consultarEstado.php con datos: {"hookalias":"SDLQU63285"}
[29-Jul-2025 22:31:54 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[29-Jul-2025 22:31:54 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[{"id":61,"hook_alias":"SDLQU63285","status":"pendiente","url":null,"fecha_creacion":{"date":"2025-07-29 17:31:34.687000","timezone_type":3,"timezone":"UTC"},"fecha_actualizacion":null,"response_code":null,"response_description":null,"amount":null,"currency":null,"installment_number":null,"description":"Compra en Tienda de Nike","date_time":null,"ticket_number":null,"authorization_code":null,"commerce_name":null,"branch_name":null,"bin":null,"merchant_code":null,"payer_name":null,"payer_lastname":null,"card_last_numbers":null,"account_type":null,"created_at":null}]}
[29-Jul-2025 22:31:59 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[29-Jul-2025 22:31:59 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[29-Jul-2025 22:31:59 Europe/Berlin] consultarEstado.php - Query String: 
[29-Jul-2025 22:31:59 Europe/Berlin] consultarEstado.php - GET params: []
[29-Jul-2025 22:31:59 Europe/Berlin] consultarEstado.php - POST params: []
[29-Jul-2025 22:31:59 Europe/Berlin] consultarEstado.php - Input raw length: 26
[29-Jul-2025 22:31:59 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SDLQU63285"}'
[29-Jul-2025 22:31:59 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SDLQU63285"}
[29-Jul-2025 22:31:59 Europe/Berlin] consultarEstado.php - JSON last error: No error
[29-Jul-2025 22:31:59 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SDLQU63285
[29-Jul-2025 22:31:59 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/bancard/consultarEstado.php con datos: {"hookalias":"SDLQU63285"}
[29-Jul-2025 22:31:59 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[29-Jul-2025 22:31:59 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[{"id":61,"hook_alias":"SDLQU63285","status":"pendiente","url":null,"fecha_creacion":{"date":"2025-07-29 17:31:34.687000","timezone_type":3,"timezone":"UTC"},"fecha_actualizacion":null,"response_code":null,"response_description":null,"amount":null,"currency":null,"installment_number":null,"description":"Compra en Tienda de Nike","date_time":null,"ticket_number":null,"authorization_code":null,"commerce_name":null,"branch_name":null,"bin":null,"merchant_code":null,"payer_name":null,"payer_lastname":null,"card_last_numbers":null,"account_type":null,"created_at":null}]}
[29-Jul-2025 22:32:04 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[29-Jul-2025 22:32:04 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[29-Jul-2025 22:32:04 Europe/Berlin] consultarEstado.php - Query String: 
[29-Jul-2025 22:32:04 Europe/Berlin] consultarEstado.php - GET params: []
[29-Jul-2025 22:32:04 Europe/Berlin] consultarEstado.php - POST params: []
[29-Jul-2025 22:32:04 Europe/Berlin] consultarEstado.php - Input raw length: 26
[29-Jul-2025 22:32:04 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SDLQU63285"}'
[29-Jul-2025 22:32:04 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SDLQU63285"}
[29-Jul-2025 22:32:04 Europe/Berlin] consultarEstado.php - JSON last error: No error
[29-Jul-2025 22:32:04 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SDLQU63285
[29-Jul-2025 22:32:04 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/bancard/consultarEstado.php con datos: {"hookalias":"SDLQU63285"}
[29-Jul-2025 22:32:04 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[29-Jul-2025 22:32:04 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[{"id":61,"hook_alias":"SDLQU63285","status":"pendiente","url":null,"fecha_creacion":{"date":"2025-07-29 17:31:34.687000","timezone_type":3,"timezone":"UTC"},"fecha_actualizacion":null,"response_code":null,"response_description":null,"amount":null,"currency":null,"installment_number":null,"description":"Compra en Tienda de Nike","date_time":null,"ticket_number":null,"authorization_code":null,"commerce_name":null,"branch_name":null,"bin":null,"merchant_code":null,"payer_name":null,"payer_lastname":null,"card_last_numbers":null,"account_type":null,"created_at":null}]}
[29-Jul-2025 22:32:09 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[29-Jul-2025 22:32:09 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[29-Jul-2025 22:32:09 Europe/Berlin] consultarEstado.php - Query String: 
[29-Jul-2025 22:32:09 Europe/Berlin] consultarEstado.php - GET params: []
[29-Jul-2025 22:32:09 Europe/Berlin] consultarEstado.php - POST params: []
[29-Jul-2025 22:32:09 Europe/Berlin] consultarEstado.php - Input raw length: 26
[29-Jul-2025 22:32:09 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SDLQU63285"}'
[29-Jul-2025 22:32:09 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SDLQU63285"}
[29-Jul-2025 22:32:09 Europe/Berlin] consultarEstado.php - JSON last error: No error
[29-Jul-2025 22:32:09 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SDLQU63285
[29-Jul-2025 22:32:09 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/bancard/consultarEstado.php con datos: {"hookalias":"SDLQU63285"}
[29-Jul-2025 22:32:09 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[29-Jul-2025 22:32:09 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[{"id":61,"hook_alias":"SDLQU63285","status":"pendiente","url":null,"fecha_creacion":{"date":"2025-07-29 17:31:34.687000","timezone_type":3,"timezone":"UTC"},"fecha_actualizacion":null,"response_code":null,"response_description":null,"amount":null,"currency":null,"installment_number":null,"description":"Compra en Tienda de Nike","date_time":null,"ticket_number":null,"authorization_code":null,"commerce_name":null,"branch_name":null,"bin":null,"merchant_code":null,"payer_name":null,"payer_lastname":null,"card_last_numbers":null,"account_type":null,"created_at":null}]}
[29-Jul-2025 22:32:14 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[29-Jul-2025 22:32:14 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[29-Jul-2025 22:32:14 Europe/Berlin] consultarEstado.php - Query String: 
[29-Jul-2025 22:32:14 Europe/Berlin] consultarEstado.php - GET params: []
[29-Jul-2025 22:32:14 Europe/Berlin] consultarEstado.php - POST params: []
[29-Jul-2025 22:32:14 Europe/Berlin] consultarEstado.php - Input raw length: 26
[29-Jul-2025 22:32:14 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SDLQU63285"}'
[29-Jul-2025 22:32:14 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SDLQU63285"}
[29-Jul-2025 22:32:14 Europe/Berlin] consultarEstado.php - JSON last error: No error
[29-Jul-2025 22:32:14 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SDLQU63285
[29-Jul-2025 22:32:14 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/bancard/consultarEstado.php con datos: {"hookalias":"SDLQU63285"}
[29-Jul-2025 22:32:15 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[29-Jul-2025 22:32:15 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[{"id":61,"hook_alias":"SDLQU63285","status":"pendiente","url":null,"fecha_creacion":{"date":"2025-07-29 17:31:34.687000","timezone_type":3,"timezone":"UTC"},"fecha_actualizacion":null,"response_code":null,"response_description":null,"amount":null,"currency":null,"installment_number":null,"description":"Compra en Tienda de Nike","date_time":null,"ticket_number":null,"authorization_code":null,"commerce_name":null,"branch_name":null,"bin":null,"merchant_code":null,"payer_name":null,"payer_lastname":null,"card_last_numbers":null,"account_type":null,"created_at":null}]}
[29-Jul-2025 22:32:19 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[29-Jul-2025 22:32:19 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[29-Jul-2025 22:32:19 Europe/Berlin] consultarEstado.php - Query String: 
[29-Jul-2025 22:32:19 Europe/Berlin] consultarEstado.php - GET params: []
[29-Jul-2025 22:32:19 Europe/Berlin] consultarEstado.php - POST params: []
[29-Jul-2025 22:32:19 Europe/Berlin] consultarEstado.php - Input raw length: 26
[29-Jul-2025 22:32:19 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SDLQU63285"}'
[29-Jul-2025 22:32:19 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SDLQU63285"}
[29-Jul-2025 22:32:19 Europe/Berlin] consultarEstado.php - JSON last error: No error
[29-Jul-2025 22:32:19 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SDLQU63285
[29-Jul-2025 22:32:19 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/bancard/consultarEstado.php con datos: {"hookalias":"SDLQU63285"}
[29-Jul-2025 22:32:19 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[29-Jul-2025 22:32:19 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[{"id":61,"hook_alias":"SDLQU63285","status":"pendiente","url":null,"fecha_creacion":{"date":"2025-07-29 17:31:34.687000","timezone_type":3,"timezone":"UTC"},"fecha_actualizacion":null,"response_code":null,"response_description":null,"amount":null,"currency":null,"installment_number":null,"description":"Compra en Tienda de Nike","date_time":null,"ticket_number":null,"authorization_code":null,"commerce_name":null,"branch_name":null,"bin":null,"merchant_code":null,"payer_name":null,"payer_lastname":null,"card_last_numbers":null,"account_type":null,"created_at":null}]}
[29-Jul-2025 22:32:24 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[29-Jul-2025 22:32:24 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[29-Jul-2025 22:32:24 Europe/Berlin] consultarEstado.php - Query String: 
[29-Jul-2025 22:32:24 Europe/Berlin] consultarEstado.php - GET params: []
[29-Jul-2025 22:32:24 Europe/Berlin] consultarEstado.php - POST params: []
[29-Jul-2025 22:32:24 Europe/Berlin] consultarEstado.php - Input raw length: 26
[29-Jul-2025 22:32:24 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SDLQU63285"}'
[29-Jul-2025 22:32:24 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SDLQU63285"}
[29-Jul-2025 22:32:24 Europe/Berlin] consultarEstado.php - JSON last error: No error
[29-Jul-2025 22:32:24 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SDLQU63285
[29-Jul-2025 22:32:24 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/bancard/consultarEstado.php con datos: {"hookalias":"SDLQU63285"}
[29-Jul-2025 22:32:24 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[29-Jul-2025 22:32:24 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[{"id":61,"hook_alias":"SDLQU63285","status":"pendiente","url":null,"fecha_creacion":{"date":"2025-07-29 17:31:34.687000","timezone_type":3,"timezone":"UTC"},"fecha_actualizacion":null,"response_code":null,"response_description":null,"amount":null,"currency":null,"installment_number":null,"description":"Compra en Tienda de Nike","date_time":null,"ticket_number":null,"authorization_code":null,"commerce_name":null,"branch_name":null,"bin":null,"merchant_code":null,"payer_name":null,"payer_lastname":null,"card_last_numbers":null,"account_type":null,"created_at":null}]}
[29-Jul-2025 22:32:29 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[29-Jul-2025 22:32:29 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[29-Jul-2025 22:32:29 Europe/Berlin] consultarEstado.php - Query String: 
[29-Jul-2025 22:32:29 Europe/Berlin] consultarEstado.php - GET params: []
[29-Jul-2025 22:32:29 Europe/Berlin] consultarEstado.php - POST params: []
[29-Jul-2025 22:32:29 Europe/Berlin] consultarEstado.php - Input raw length: 26
[29-Jul-2025 22:32:29 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SDLQU63285"}'
[29-Jul-2025 22:32:29 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SDLQU63285"}
[29-Jul-2025 22:32:29 Europe/Berlin] consultarEstado.php - JSON last error: No error
[29-Jul-2025 22:32:29 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SDLQU63285
[29-Jul-2025 22:32:29 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/bancard/consultarEstado.php con datos: {"hookalias":"SDLQU63285"}
[29-Jul-2025 22:32:29 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[29-Jul-2025 22:32:29 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[{"id":61,"hook_alias":"SDLQU63285","status":"pendiente","url":null,"fecha_creacion":{"date":"2025-07-29 17:31:34.687000","timezone_type":3,"timezone":"UTC"},"fecha_actualizacion":null,"response_code":null,"response_description":null,"amount":null,"currency":null,"installment_number":null,"description":"Compra en Tienda de Nike","date_time":null,"ticket_number":null,"authorization_code":null,"commerce_name":null,"branch_name":null,"bin":null,"merchant_code":null,"payer_name":null,"payer_lastname":null,"card_last_numbers":null,"account_type":null,"created_at":null}]}
[29-Jul-2025 22:32:34 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[29-Jul-2025 22:32:34 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[29-Jul-2025 22:32:34 Europe/Berlin] consultarEstado.php - Query String: 
[29-Jul-2025 22:32:34 Europe/Berlin] consultarEstado.php - GET params: []
[29-Jul-2025 22:32:34 Europe/Berlin] consultarEstado.php - POST params: []
[29-Jul-2025 22:32:34 Europe/Berlin] consultarEstado.php - Input raw length: 26
[29-Jul-2025 22:32:34 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SDLQU63285"}'
[29-Jul-2025 22:32:34 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SDLQU63285"}
[29-Jul-2025 22:32:34 Europe/Berlin] consultarEstado.php - JSON last error: No error
[29-Jul-2025 22:32:34 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SDLQU63285
[29-Jul-2025 22:32:34 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/bancard/consultarEstado.php con datos: {"hookalias":"SDLQU63285"}
[29-Jul-2025 22:32:34 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[29-Jul-2025 22:32:34 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[{"id":61,"hook_alias":"SDLQU63285","status":"pendiente","url":null,"fecha_creacion":{"date":"2025-07-29 17:31:34.687000","timezone_type":3,"timezone":"UTC"},"fecha_actualizacion":null,"response_code":null,"response_description":null,"amount":null,"currency":null,"installment_number":null,"description":"Compra en Tienda de Nike","date_time":null,"ticket_number":null,"authorization_code":null,"commerce_name":null,"branch_name":null,"bin":null,"merchant_code":null,"payer_name":null,"payer_lastname":null,"card_last_numbers":null,"account_type":null,"created_at":null}]}
[29-Jul-2025 22:32:39 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[29-Jul-2025 22:32:39 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[29-Jul-2025 22:32:39 Europe/Berlin] consultarEstado.php - Query String: 
[29-Jul-2025 22:32:39 Europe/Berlin] consultarEstado.php - GET params: []
[29-Jul-2025 22:32:39 Europe/Berlin] consultarEstado.php - POST params: []
[29-Jul-2025 22:32:39 Europe/Berlin] consultarEstado.php - Input raw length: 26
[29-Jul-2025 22:32:39 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SDLQU63285"}'
[29-Jul-2025 22:32:39 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SDLQU63285"}
[29-Jul-2025 22:32:39 Europe/Berlin] consultarEstado.php - JSON last error: No error
[29-Jul-2025 22:32:39 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SDLQU63285
[29-Jul-2025 22:32:39 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/bancard/consultarEstado.php con datos: {"hookalias":"SDLQU63285"}
[29-Jul-2025 22:32:39 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[29-Jul-2025 22:32:39 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[{"id":61,"hook_alias":"SDLQU63285","status":"pendiente","url":null,"fecha_creacion":{"date":"2025-07-29 17:31:34.687000","timezone_type":3,"timezone":"UTC"},"fecha_actualizacion":null,"response_code":null,"response_description":null,"amount":null,"currency":null,"installment_number":null,"description":"Compra en Tienda de Nike","date_time":null,"ticket_number":null,"authorization_code":null,"commerce_name":null,"branch_name":null,"bin":null,"merchant_code":null,"payer_name":null,"payer_lastname":null,"card_last_numbers":null,"account_type":null,"created_at":null}]}
[29-Jul-2025 22:32:44 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[29-Jul-2025 22:32:44 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[29-Jul-2025 22:32:44 Europe/Berlin] consultarEstado.php - Query String: 
[29-Jul-2025 22:32:44 Europe/Berlin] consultarEstado.php - GET params: []
[29-Jul-2025 22:32:44 Europe/Berlin] consultarEstado.php - POST params: []
[29-Jul-2025 22:32:44 Europe/Berlin] consultarEstado.php - Input raw length: 26
[29-Jul-2025 22:32:44 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SDLQU63285"}'
[29-Jul-2025 22:32:44 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SDLQU63285"}
[29-Jul-2025 22:32:44 Europe/Berlin] consultarEstado.php - JSON last error: No error
[29-Jul-2025 22:32:44 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SDLQU63285
[29-Jul-2025 22:32:44 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/bancard/consultarEstado.php con datos: {"hookalias":"SDLQU63285"}
[29-Jul-2025 22:32:44 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[29-Jul-2025 22:32:44 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[{"id":61,"hook_alias":"SDLQU63285","status":"pendiente","url":null,"fecha_creacion":{"date":"2025-07-29 17:31:34.687000","timezone_type":3,"timezone":"UTC"},"fecha_actualizacion":null,"response_code":null,"response_description":null,"amount":null,"currency":null,"installment_number":null,"description":"Compra en Tienda de Nike","date_time":null,"ticket_number":null,"authorization_code":null,"commerce_name":null,"branch_name":null,"bin":null,"merchant_code":null,"payer_name":null,"payer_lastname":null,"card_last_numbers":null,"account_type":null,"created_at":null}]}
[29-Jul-2025 22:32:49 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[29-Jul-2025 22:32:49 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[29-Jul-2025 22:32:49 Europe/Berlin] consultarEstado.php - Query String: 
[29-Jul-2025 22:32:49 Europe/Berlin] consultarEstado.php - GET params: []
[29-Jul-2025 22:32:49 Europe/Berlin] consultarEstado.php - POST params: []
[29-Jul-2025 22:32:49 Europe/Berlin] consultarEstado.php - Input raw length: 26
[29-Jul-2025 22:32:49 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SDLQU63285"}'
[29-Jul-2025 22:32:49 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SDLQU63285"}
[29-Jul-2025 22:32:49 Europe/Berlin] consultarEstado.php - JSON last error: No error
[29-Jul-2025 22:32:49 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SDLQU63285
[29-Jul-2025 22:32:49 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/bancard/consultarEstado.php con datos: {"hookalias":"SDLQU63285"}
[29-Jul-2025 22:32:49 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[29-Jul-2025 22:32:49 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[{"id":61,"hook_alias":"SDLQU63285","status":"pendiente","url":null,"fecha_creacion":{"date":"2025-07-29 17:31:34.687000","timezone_type":3,"timezone":"UTC"},"fecha_actualizacion":null,"response_code":null,"response_description":null,"amount":null,"currency":null,"installment_number":null,"description":"Compra en Tienda de Nike","date_time":null,"ticket_number":null,"authorization_code":null,"commerce_name":null,"branch_name":null,"bin":null,"merchant_code":null,"payer_name":null,"payer_lastname":null,"card_last_numbers":null,"account_type":null,"created_at":null}]}
[29-Jul-2025 22:32:54 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[29-Jul-2025 22:32:54 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[29-Jul-2025 22:32:54 Europe/Berlin] consultarEstado.php - Query String: 
[29-Jul-2025 22:32:54 Europe/Berlin] consultarEstado.php - GET params: []
[29-Jul-2025 22:32:54 Europe/Berlin] consultarEstado.php - POST params: []
[29-Jul-2025 22:32:54 Europe/Berlin] consultarEstado.php - Input raw length: 26
[29-Jul-2025 22:32:54 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SDLQU63285"}'
[29-Jul-2025 22:32:54 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SDLQU63285"}
[29-Jul-2025 22:32:54 Europe/Berlin] consultarEstado.php - JSON last error: No error
[29-Jul-2025 22:32:54 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SDLQU63285
[29-Jul-2025 22:32:54 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/bancard/consultarEstado.php con datos: {"hookalias":"SDLQU63285"}
[29-Jul-2025 22:32:54 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[29-Jul-2025 22:32:54 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[{"id":61,"hook_alias":"SDLQU63285","status":"pendiente","url":null,"fecha_creacion":{"date":"2025-07-29 17:31:34.687000","timezone_type":3,"timezone":"UTC"},"fecha_actualizacion":null,"response_code":null,"response_description":null,"amount":null,"currency":null,"installment_number":null,"description":"Compra en Tienda de Nike","date_time":null,"ticket_number":null,"authorization_code":null,"commerce_name":null,"branch_name":null,"bin":null,"merchant_code":null,"payer_name":null,"payer_lastname":null,"card_last_numbers":null,"account_type":null,"created_at":null}]}
[29-Jul-2025 22:32:59 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[29-Jul-2025 22:32:59 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[29-Jul-2025 22:32:59 Europe/Berlin] consultarEstado.php - Query String: 
[29-Jul-2025 22:32:59 Europe/Berlin] consultarEstado.php - GET params: []
[29-Jul-2025 22:32:59 Europe/Berlin] consultarEstado.php - POST params: []
[29-Jul-2025 22:32:59 Europe/Berlin] consultarEstado.php - Input raw length: 26
[29-Jul-2025 22:32:59 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SDLQU63285"}'
[29-Jul-2025 22:32:59 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SDLQU63285"}
[29-Jul-2025 22:32:59 Europe/Berlin] consultarEstado.php - JSON last error: No error
[29-Jul-2025 22:32:59 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SDLQU63285
[29-Jul-2025 22:32:59 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/bancard/consultarEstado.php con datos: {"hookalias":"SDLQU63285"}
[29-Jul-2025 22:32:59 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[29-Jul-2025 22:32:59 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[{"id":61,"hook_alias":"SDLQU63285","status":"pendiente","url":null,"fecha_creacion":{"date":"2025-07-29 17:31:34.687000","timezone_type":3,"timezone":"UTC"},"fecha_actualizacion":null,"response_code":null,"response_description":null,"amount":null,"currency":null,"installment_number":null,"description":"Compra en Tienda de Nike","date_time":null,"ticket_number":null,"authorization_code":null,"commerce_name":null,"branch_name":null,"bin":null,"merchant_code":null,"payer_name":null,"payer_lastname":null,"card_last_numbers":null,"account_type":null,"created_at":null}]}
[29-Jul-2025 22:33:04 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[29-Jul-2025 22:33:04 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[29-Jul-2025 22:33:04 Europe/Berlin] consultarEstado.php - Query String: 
[29-Jul-2025 22:33:04 Europe/Berlin] consultarEstado.php - GET params: []
[29-Jul-2025 22:33:04 Europe/Berlin] consultarEstado.php - POST params: []
[29-Jul-2025 22:33:04 Europe/Berlin] consultarEstado.php - Input raw length: 26
[29-Jul-2025 22:33:04 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SDLQU63285"}'
[29-Jul-2025 22:33:04 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SDLQU63285"}
[29-Jul-2025 22:33:04 Europe/Berlin] consultarEstado.php - JSON last error: No error
[29-Jul-2025 22:33:04 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SDLQU63285
[29-Jul-2025 22:33:04 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/bancard/consultarEstado.php con datos: {"hookalias":"SDLQU63285"}
[29-Jul-2025 22:33:04 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[29-Jul-2025 22:33:04 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[{"id":61,"hook_alias":"SDLQU63285","status":"pendiente","url":null,"fecha_creacion":{"date":"2025-07-29 17:31:34.687000","timezone_type":3,"timezone":"UTC"},"fecha_actualizacion":null,"response_code":null,"response_description":null,"amount":null,"currency":null,"installment_number":null,"description":"Compra en Tienda de Nike","date_time":null,"ticket_number":null,"authorization_code":null,"commerce_name":null,"branch_name":null,"bin":null,"merchant_code":null,"payer_name":null,"payer_lastname":null,"card_last_numbers":null,"account_type":null,"created_at":null}]}
[29-Jul-2025 22:33:09 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[29-Jul-2025 22:33:09 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[29-Jul-2025 22:33:09 Europe/Berlin] consultarEstado.php - Query String: 
[29-Jul-2025 22:33:09 Europe/Berlin] consultarEstado.php - GET params: []
[29-Jul-2025 22:33:09 Europe/Berlin] consultarEstado.php - POST params: []
[29-Jul-2025 22:33:09 Europe/Berlin] consultarEstado.php - Input raw length: 26
[29-Jul-2025 22:33:09 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SDLQU63285"}'
[29-Jul-2025 22:33:09 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SDLQU63285"}
[29-Jul-2025 22:33:09 Europe/Berlin] consultarEstado.php - JSON last error: No error
[29-Jul-2025 22:33:09 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SDLQU63285
[29-Jul-2025 22:33:09 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/bancard/consultarEstado.php con datos: {"hookalias":"SDLQU63285"}
[29-Jul-2025 22:33:09 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[29-Jul-2025 22:33:09 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[{"id":61,"hook_alias":"SDLQU63285","status":"pendiente","url":null,"fecha_creacion":{"date":"2025-07-29 17:31:34.687000","timezone_type":3,"timezone":"UTC"},"fecha_actualizacion":null,"response_code":null,"response_description":null,"amount":null,"currency":null,"installment_number":null,"description":"Compra en Tienda de Nike","date_time":null,"ticket_number":null,"authorization_code":null,"commerce_name":null,"branch_name":null,"bin":null,"merchant_code":null,"payer_name":null,"payer_lastname":null,"card_last_numbers":null,"account_type":null,"created_at":null}]}
[30-Jul-2025 14:07:40 Europe/Berlin] [TEST CONNECTION INIT] Parámetros recibidos - TestPC: ************** | CodeEmp: 1 | CodSuc: 33
[30-Jul-2025 14:07:40 Europe/Berlin] [TEST CONNECTION DYNAMIC] TestPC no encontrado, construyendo dinámicamente | Servidor construido: **************.swoosh.net | Base de datos: SUC33 | Usuario: SA | Empresa: zavidoro | Dominio: swoosh.net
[30-Jul-2025 14:07:40 Europe/Berlin] [TEST CONNECTION] Tipo: Prueba Dinámica (TESTPC:**************, CODEMP:1, CODSUC:33) | Servidor: **************.swoosh.net | Base de datos: SUC33 | Usuario: SA | Timestamp: 2025-07-30 14:07:40
[30-Jul-2025 14:07:49 Europe/Berlin] registrarAlias.php - Método HTTP: PUT
[30-Jul-2025 14:07:49 Europe/Berlin] registrarAlias.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 14:07:49 Europe/Berlin] registrarAlias.php - Query String: 
[30-Jul-2025 14:07:49 Europe/Berlin] registrarAlias.php - GET params: []
[30-Jul-2025 14:07:49 Europe/Berlin] registrarAlias.php - POST params: []
[30-Jul-2025 14:07:49 Europe/Berlin] registrarAlias.php - Input raw length: 120
[30-Jul-2025 14:07:49 Europe/Berlin] registrarAlias.php - Input raw: '{"hookalias":"SXLUG52104","amount":110850,"description":"Compra en Tienda de Nike","created_at":"30\/07\/2025 09:07:48"}'
[30-Jul-2025 14:07:49 Europe/Berlin] registrarAlias.php - JSON decode result: {"hookalias":"SXLUG52104","amount":110850,"description":"Compra en Tienda de Nike","created_at":"30\/07\/2025 09:07:48"}
[30-Jul-2025 14:07:49 Europe/Berlin] registrarAlias.php - JSON last error: No error
[30-Jul-2025 14:07:49 Europe/Berlin] registrarAlias.php - hookalias encontrado en JSON body: SXLUG52104
[30-Jul-2025 14:07:49 Europe/Berlin] registrarAlias.php - amount encontrado en JSON body: 110850
[30-Jul-2025 14:07:49 Europe/Berlin] registrarAlias.php - description encontrado en JSON body: Compra en Tienda de Nike
[30-Jul-2025 14:07:49 Europe/Berlin] registrarAlias.php - created_at encontrado en JSON body: 30/07/2025 09:07:48
[30-Jul-2025 14:07:49 Europe/Berlin] registrarAlias.php - Enviando a: https://zavidoro.com.py/bancard/registrarAlias.php con datos: {"hookalias":"SXLUG52104","amount":110850,"description":"Compra en Tienda de Nike","created_at":"30\/07\/2025 09:07:48"}
[30-Jul-2025 14:07:49 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 14:07:49 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 14:07:49 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 14:07:49 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 14:07:49 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 14:07:49 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 14:07:49 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SXLUG52104"}'
[30-Jul-2025 14:07:49 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SXLUG52104"}
[30-Jul-2025 14:07:49 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 14:07:49 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SXLUG52104
[30-Jul-2025 14:07:49 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/bancard/consultarEstado.php con datos: {"hookalias":"SXLUG52104"}
[30-Jul-2025 14:07:50 Europe/Berlin] registrarAlias.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 14:07:50 Europe/Berlin] registrarAlias.php - Respuesta del servidor remoto - Body: {"status":"success","message":"Alias registrado correctamente"}
[30-Jul-2025 14:07:51 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 14:07:51 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[{"id":62,"hook_alias":"SXLUG52104","status":"pendiente","url":null,"fecha_creacion":{"date":"2025-07-30 09:07:50.013000","timezone_type":3,"timezone":"UTC"},"fecha_actualizacion":null,"response_code":null,"response_description":null,"amount":null,"currency":null,"installment_number":null,"description":"Compra en Tienda de Nike","date_time":null,"ticket_number":null,"authorization_code":null,"commerce_name":null,"branch_name":null,"bin":null,"merchant_code":null,"payer_name":null,"payer_lastname":null,"card_last_numbers":null,"account_type":null,"created_at":null}]}
[30-Jul-2025 14:07:54 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 14:07:54 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 14:07:54 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 14:07:54 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 14:07:54 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 14:07:54 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 14:07:54 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SXLUG52104"}'
[30-Jul-2025 14:07:54 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SXLUG52104"}
[30-Jul-2025 14:07:54 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 14:07:54 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SXLUG52104
[30-Jul-2025 14:07:54 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/bancard/consultarEstado.php con datos: {"hookalias":"SXLUG52104"}
[30-Jul-2025 14:07:55 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 14:07:55 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[{"id":62,"hook_alias":"SXLUG52104","status":"pendiente","url":null,"fecha_creacion":{"date":"2025-07-30 09:07:50.013000","timezone_type":3,"timezone":"UTC"},"fecha_actualizacion":null,"response_code":null,"response_description":null,"amount":null,"currency":null,"installment_number":null,"description":"Compra en Tienda de Nike","date_time":null,"ticket_number":null,"authorization_code":null,"commerce_name":null,"branch_name":null,"bin":null,"merchant_code":null,"payer_name":null,"payer_lastname":null,"card_last_numbers":null,"account_type":null,"created_at":null}]}
[30-Jul-2025 14:07:55 Europe/Berlin] [TEST CONNECTION ERROR] Prueba Dinámica (TESTPC:**************, CODEMP:1, CODSUC:33) | Servidor intentado: **************.swoosh.net | Base de datos: SUC33 | Error: SQLSTATE: 08001, Code: 53, Message: [Microsoft][ODBC Driver 17 for SQL Server]Named Pipes Provider: Could not open a connection to SQL Server [53]. ; SQLSTATE: HYT00, Code: 0, Message: [Microsoft][ODBC Driver 17 for SQL Server]Login timeout expired; SQLSTATE: 08001, Code: 53, Message: [Microsoft][ODBC Driver 17 for SQL Server]A network-related or instance-specific error has occurred while establishing a connection to SQL Server. Server is not found or not accessible. Check if instance name is correct and if SQL Server is configured to allow remote connections. For more information see SQL Server Books Online.
[30-Jul-2025 14:07:55 Europe/Berlin] Falló la conexión Prueba Dinámica (TESTPC:**************, CODEMP:1, CODSUC:33): SQLSTATE: 08001, Code: 53, Message: [Microsoft][ODBC Driver 17 for SQL Server]Named Pipes Provider: Could not open a connection to SQL Server [53]. ; SQLSTATE: HYT00, Code: 0, Message: [Microsoft][ODBC Driver 17 for SQL Server]Login timeout expired; SQLSTATE: 08001, Code: 53, Message: [Microsoft][ODBC Driver 17 for SQL Server]A network-related or instance-specific error has occurred while establishing a connection to SQL Server. Server is not found or not accessible. Check if instance name is correct and if SQL Server is configured to allow remote connections. For more information see SQL Server Books Online.
[30-Jul-2025 14:07:59 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 14:07:59 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 14:07:59 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 14:07:59 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 14:07:59 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 14:07:59 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 14:07:59 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SXLUG52104"}'
[30-Jul-2025 14:07:59 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SXLUG52104"}
[30-Jul-2025 14:07:59 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 14:07:59 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SXLUG52104
[30-Jul-2025 14:07:59 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/bancard/consultarEstado.php con datos: {"hookalias":"SXLUG52104"}
[30-Jul-2025 14:08:00 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 14:08:00 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[{"id":62,"hook_alias":"SXLUG52104","status":"pendiente","url":null,"fecha_creacion":{"date":"2025-07-30 09:07:50.013000","timezone_type":3,"timezone":"UTC"},"fecha_actualizacion":null,"response_code":null,"response_description":null,"amount":null,"currency":null,"installment_number":null,"description":"Compra en Tienda de Nike","date_time":null,"ticket_number":null,"authorization_code":null,"commerce_name":null,"branch_name":null,"bin":null,"merchant_code":null,"payer_name":null,"payer_lastname":null,"card_last_numbers":null,"account_type":null,"created_at":null}]}
[30-Jul-2025 14:08:04 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 14:08:04 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 14:08:04 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 14:08:04 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 14:08:04 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 14:08:04 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 14:08:04 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SXLUG52104"}'
[30-Jul-2025 14:08:04 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SXLUG52104"}
[30-Jul-2025 14:08:04 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 14:08:04 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SXLUG52104
[30-Jul-2025 14:08:04 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/bancard/consultarEstado.php con datos: {"hookalias":"SXLUG52104"}
[30-Jul-2025 14:08:05 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 14:08:05 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[{"id":62,"hook_alias":"SXLUG52104","status":"pendiente","url":null,"fecha_creacion":{"date":"2025-07-30 09:07:50.013000","timezone_type":3,"timezone":"UTC"},"fecha_actualizacion":null,"response_code":null,"response_description":null,"amount":null,"currency":null,"installment_number":null,"description":"Compra en Tienda de Nike","date_time":null,"ticket_number":null,"authorization_code":null,"commerce_name":null,"branch_name":null,"bin":null,"merchant_code":null,"payer_name":null,"payer_lastname":null,"card_last_numbers":null,"account_type":null,"created_at":null}]}
[30-Jul-2025 14:08:09 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 14:08:09 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 14:08:09 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 14:08:09 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 14:08:09 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 14:08:09 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 14:08:09 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SXLUG52104"}'
[30-Jul-2025 14:08:09 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SXLUG52104"}
[30-Jul-2025 14:08:09 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 14:08:09 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SXLUG52104
[30-Jul-2025 14:08:09 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/bancard/consultarEstado.php con datos: {"hookalias":"SXLUG52104"}
[30-Jul-2025 14:08:10 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 14:08:10 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[{"id":62,"hook_alias":"SXLUG52104","status":"pendiente","url":null,"fecha_creacion":{"date":"2025-07-30 09:07:50.013000","timezone_type":3,"timezone":"UTC"},"fecha_actualizacion":null,"response_code":null,"response_description":null,"amount":null,"currency":null,"installment_number":null,"description":"Compra en Tienda de Nike","date_time":null,"ticket_number":null,"authorization_code":null,"commerce_name":null,"branch_name":null,"bin":null,"merchant_code":null,"payer_name":null,"payer_lastname":null,"card_last_numbers":null,"account_type":null,"created_at":null}]}
[30-Jul-2025 14:08:14 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 14:08:14 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 14:08:14 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 14:08:14 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 14:08:14 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 14:08:14 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 14:08:14 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SXLUG52104"}'
[30-Jul-2025 14:08:14 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SXLUG52104"}
[30-Jul-2025 14:08:14 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 14:08:14 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SXLUG52104
[30-Jul-2025 14:08:14 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/bancard/consultarEstado.php con datos: {"hookalias":"SXLUG52104"}
[30-Jul-2025 14:08:15 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 14:08:15 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[{"id":62,"hook_alias":"SXLUG52104","status":"pendiente","url":null,"fecha_creacion":{"date":"2025-07-30 09:07:50.013000","timezone_type":3,"timezone":"UTC"},"fecha_actualizacion":null,"response_code":null,"response_description":null,"amount":null,"currency":null,"installment_number":null,"description":"Compra en Tienda de Nike","date_time":null,"ticket_number":null,"authorization_code":null,"commerce_name":null,"branch_name":null,"bin":null,"merchant_code":null,"payer_name":null,"payer_lastname":null,"card_last_numbers":null,"account_type":null,"created_at":null}]}
[30-Jul-2025 14:08:19 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 14:08:19 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 14:08:19 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 14:08:19 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 14:08:19 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 14:08:19 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 14:08:19 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SXLUG52104"}'
[30-Jul-2025 14:08:19 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SXLUG52104"}
[30-Jul-2025 14:08:19 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 14:08:19 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SXLUG52104
[30-Jul-2025 14:08:19 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/bancard/consultarEstado.php con datos: {"hookalias":"SXLUG52104"}
[30-Jul-2025 14:08:20 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 14:08:20 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[{"id":62,"hook_alias":"SXLUG52104","status":"pendiente","url":null,"fecha_creacion":{"date":"2025-07-30 09:07:50.013000","timezone_type":3,"timezone":"UTC"},"fecha_actualizacion":null,"response_code":null,"response_description":null,"amount":null,"currency":null,"installment_number":null,"description":"Compra en Tienda de Nike","date_time":null,"ticket_number":null,"authorization_code":null,"commerce_name":null,"branch_name":null,"bin":null,"merchant_code":null,"payer_name":null,"payer_lastname":null,"card_last_numbers":null,"account_type":null,"created_at":null}]}
[30-Jul-2025 14:08:24 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 14:08:24 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 14:08:24 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 14:08:24 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 14:08:24 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 14:08:24 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 14:08:24 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SXLUG52104"}'
[30-Jul-2025 14:08:24 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SXLUG52104"}
[30-Jul-2025 14:08:24 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 14:08:24 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SXLUG52104
[30-Jul-2025 14:08:24 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/bancard/consultarEstado.php con datos: {"hookalias":"SXLUG52104"}
[30-Jul-2025 14:08:25 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 14:08:25 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[{"id":62,"hook_alias":"SXLUG52104","status":"pendiente","url":null,"fecha_creacion":{"date":"2025-07-30 09:07:50.013000","timezone_type":3,"timezone":"UTC"},"fecha_actualizacion":null,"response_code":null,"response_description":null,"amount":null,"currency":null,"installment_number":null,"description":"Compra en Tienda de Nike","date_time":null,"ticket_number":null,"authorization_code":null,"commerce_name":null,"branch_name":null,"bin":null,"merchant_code":null,"payer_name":null,"payer_lastname":null,"card_last_numbers":null,"account_type":null,"created_at":null}]}
[30-Jul-2025 14:08:29 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 14:08:29 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 14:08:29 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 14:08:29 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 14:08:29 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 14:08:29 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 14:08:29 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SXLUG52104"}'
[30-Jul-2025 14:08:29 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SXLUG52104"}
[30-Jul-2025 14:08:29 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 14:08:29 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SXLUG52104
[30-Jul-2025 14:08:29 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/bancard/consultarEstado.php con datos: {"hookalias":"SXLUG52104"}
[30-Jul-2025 14:08:30 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 14:08:30 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[{"id":62,"hook_alias":"SXLUG52104","status":"pendiente","url":null,"fecha_creacion":{"date":"2025-07-30 09:07:50.013000","timezone_type":3,"timezone":"UTC"},"fecha_actualizacion":null,"response_code":null,"response_description":null,"amount":null,"currency":null,"installment_number":null,"description":"Compra en Tienda de Nike","date_time":null,"ticket_number":null,"authorization_code":null,"commerce_name":null,"branch_name":null,"bin":null,"merchant_code":null,"payer_name":null,"payer_lastname":null,"card_last_numbers":null,"account_type":null,"created_at":null}]}
[30-Jul-2025 14:08:34 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 14:08:34 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 14:08:34 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 14:08:34 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 14:08:34 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 14:08:34 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 14:08:34 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SXLUG52104"}'
[30-Jul-2025 14:08:34 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SXLUG52104"}
[30-Jul-2025 14:08:34 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 14:08:34 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SXLUG52104
[30-Jul-2025 14:08:34 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/bancard/consultarEstado.php con datos: {"hookalias":"SXLUG52104"}
[30-Jul-2025 14:08:36 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 14:08:36 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[{"id":62,"hook_alias":"SXLUG52104","status":"pendiente","url":null,"fecha_creacion":{"date":"2025-07-30 09:07:50.013000","timezone_type":3,"timezone":"UTC"},"fecha_actualizacion":null,"response_code":null,"response_description":null,"amount":null,"currency":null,"installment_number":null,"description":"Compra en Tienda de Nike","date_time":null,"ticket_number":null,"authorization_code":null,"commerce_name":null,"branch_name":null,"bin":null,"merchant_code":null,"payer_name":null,"payer_lastname":null,"card_last_numbers":null,"account_type":null,"created_at":null}]}
[30-Jul-2025 14:08:39 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 14:08:39 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 14:08:39 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 14:08:39 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 14:08:39 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 14:08:39 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 14:08:39 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SXLUG52104"}'
[30-Jul-2025 14:08:39 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SXLUG52104"}
[30-Jul-2025 14:08:39 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 14:08:39 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SXLUG52104
[30-Jul-2025 14:08:39 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/bancard/consultarEstado.php con datos: {"hookalias":"SXLUG52104"}
[30-Jul-2025 14:08:40 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 14:08:40 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[{"id":62,"hook_alias":"SXLUG52104","status":"pendiente","url":null,"fecha_creacion":{"date":"2025-07-30 09:07:50.013000","timezone_type":3,"timezone":"UTC"},"fecha_actualizacion":null,"response_code":null,"response_description":null,"amount":null,"currency":null,"installment_number":null,"description":"Compra en Tienda de Nike","date_time":null,"ticket_number":null,"authorization_code":null,"commerce_name":null,"branch_name":null,"bin":null,"merchant_code":null,"payer_name":null,"payer_lastname":null,"card_last_numbers":null,"account_type":null,"created_at":null}]}
[30-Jul-2025 14:08:44 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 14:08:44 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 14:08:44 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 14:08:44 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 14:08:44 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 14:08:44 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 14:08:44 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SXLUG52104"}'
[30-Jul-2025 14:08:44 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SXLUG52104"}
[30-Jul-2025 14:08:44 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 14:08:44 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SXLUG52104
[30-Jul-2025 14:08:44 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/bancard/consultarEstado.php con datos: {"hookalias":"SXLUG52104"}
[30-Jul-2025 14:08:45 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 14:08:45 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[{"id":62,"hook_alias":"SXLUG52104","status":"pendiente","url":null,"fecha_creacion":{"date":"2025-07-30 09:07:50.013000","timezone_type":3,"timezone":"UTC"},"fecha_actualizacion":null,"response_code":null,"response_description":null,"amount":null,"currency":null,"installment_number":null,"description":"Compra en Tienda de Nike","date_time":null,"ticket_number":null,"authorization_code":null,"commerce_name":null,"branch_name":null,"bin":null,"merchant_code":null,"payer_name":null,"payer_lastname":null,"card_last_numbers":null,"account_type":null,"created_at":null}]}
[30-Jul-2025 14:08:49 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 14:08:49 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 14:08:49 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 14:08:49 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 14:08:49 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 14:08:49 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 14:08:49 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SXLUG52104"}'
[30-Jul-2025 14:08:49 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SXLUG52104"}
[30-Jul-2025 14:08:49 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 14:08:49 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SXLUG52104
[30-Jul-2025 14:08:49 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/bancard/consultarEstado.php con datos: {"hookalias":"SXLUG52104"}
[30-Jul-2025 14:08:50 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 14:08:50 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[{"id":62,"hook_alias":"SXLUG52104","status":"pendiente","url":null,"fecha_creacion":{"date":"2025-07-30 09:07:50.013000","timezone_type":3,"timezone":"UTC"},"fecha_actualizacion":null,"response_code":null,"response_description":null,"amount":null,"currency":null,"installment_number":null,"description":"Compra en Tienda de Nike","date_time":null,"ticket_number":null,"authorization_code":null,"commerce_name":null,"branch_name":null,"bin":null,"merchant_code":null,"payer_name":null,"payer_lastname":null,"card_last_numbers":null,"account_type":null,"created_at":null}]}
[30-Jul-2025 14:08:54 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 14:08:54 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 14:08:54 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 14:08:54 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 14:08:54 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 14:08:54 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 14:08:54 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SXLUG52104"}'
[30-Jul-2025 14:08:54 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SXLUG52104"}
[30-Jul-2025 14:08:54 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 14:08:54 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SXLUG52104
[30-Jul-2025 14:08:54 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/bancard/consultarEstado.php con datos: {"hookalias":"SXLUG52104"}
[30-Jul-2025 14:08:55 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 14:08:55 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[{"id":62,"hook_alias":"SXLUG52104","status":"pendiente","url":null,"fecha_creacion":{"date":"2025-07-30 09:07:50.013000","timezone_type":3,"timezone":"UTC"},"fecha_actualizacion":null,"response_code":null,"response_description":null,"amount":null,"currency":null,"installment_number":null,"description":"Compra en Tienda de Nike","date_time":null,"ticket_number":null,"authorization_code":null,"commerce_name":null,"branch_name":null,"bin":null,"merchant_code":null,"payer_name":null,"payer_lastname":null,"card_last_numbers":null,"account_type":null,"created_at":null}]}
[30-Jul-2025 14:08:59 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 14:08:59 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 14:08:59 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 14:08:59 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 14:08:59 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 14:08:59 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 14:08:59 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SXLUG52104"}'
[30-Jul-2025 14:08:59 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SXLUG52104"}
[30-Jul-2025 14:08:59 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 14:08:59 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SXLUG52104
[30-Jul-2025 14:08:59 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/bancard/consultarEstado.php con datos: {"hookalias":"SXLUG52104"}
[30-Jul-2025 14:09:00 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 14:09:00 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[{"id":62,"hook_alias":"SXLUG52104","status":"pendiente","url":null,"fecha_creacion":{"date":"2025-07-30 09:07:50.013000","timezone_type":3,"timezone":"UTC"},"fecha_actualizacion":null,"response_code":null,"response_description":null,"amount":null,"currency":null,"installment_number":null,"description":"Compra en Tienda de Nike","date_time":null,"ticket_number":null,"authorization_code":null,"commerce_name":null,"branch_name":null,"bin":null,"merchant_code":null,"payer_name":null,"payer_lastname":null,"card_last_numbers":null,"account_type":null,"created_at":null}]}
[30-Jul-2025 14:09:04 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 14:09:04 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 14:09:04 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 14:09:04 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 14:09:04 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 14:09:04 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 14:09:04 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SXLUG52104"}'
[30-Jul-2025 14:09:04 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SXLUG52104"}
[30-Jul-2025 14:09:04 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 14:09:04 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SXLUG52104
[30-Jul-2025 14:09:04 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/bancard/consultarEstado.php con datos: {"hookalias":"SXLUG52104"}
[30-Jul-2025 14:09:05 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 14:09:05 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[{"id":62,"hook_alias":"SXLUG52104","status":"pendiente","url":null,"fecha_creacion":{"date":"2025-07-30 09:07:50.013000","timezone_type":3,"timezone":"UTC"},"fecha_actualizacion":null,"response_code":null,"response_description":null,"amount":null,"currency":null,"installment_number":null,"description":"Compra en Tienda de Nike","date_time":null,"ticket_number":null,"authorization_code":null,"commerce_name":null,"branch_name":null,"bin":null,"merchant_code":null,"payer_name":null,"payer_lastname":null,"card_last_numbers":null,"account_type":null,"created_at":null}]}
[30-Jul-2025 14:09:09 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 14:09:09 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 14:09:09 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 14:09:09 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 14:09:09 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 14:09:09 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 14:09:09 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SXLUG52104"}'
[30-Jul-2025 14:09:09 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SXLUG52104"}
[30-Jul-2025 14:09:09 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 14:09:09 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SXLUG52104
[30-Jul-2025 14:09:09 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/bancard/consultarEstado.php con datos: {"hookalias":"SXLUG52104"}
[30-Jul-2025 14:09:10 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 14:09:10 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[{"id":62,"hook_alias":"SXLUG52104","status":"pendiente","url":null,"fecha_creacion":{"date":"2025-07-30 09:07:50.013000","timezone_type":3,"timezone":"UTC"},"fecha_actualizacion":null,"response_code":null,"response_description":null,"amount":null,"currency":null,"installment_number":null,"description":"Compra en Tienda de Nike","date_time":null,"ticket_number":null,"authorization_code":null,"commerce_name":null,"branch_name":null,"bin":null,"merchant_code":null,"payer_name":null,"payer_lastname":null,"card_last_numbers":null,"account_type":null,"created_at":null}]}
[30-Jul-2025 14:09:14 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 14:09:14 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 14:09:14 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 14:09:14 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 14:09:14 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 14:09:14 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 14:09:14 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SXLUG52104"}'
[30-Jul-2025 14:09:14 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SXLUG52104"}
[30-Jul-2025 14:09:14 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 14:09:14 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SXLUG52104
[30-Jul-2025 14:09:14 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/bancard/consultarEstado.php con datos: {"hookalias":"SXLUG52104"}
[30-Jul-2025 14:09:15 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 14:09:15 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[{"id":62,"hook_alias":"SXLUG52104","status":"pendiente","url":null,"fecha_creacion":{"date":"2025-07-30 09:07:50.013000","timezone_type":3,"timezone":"UTC"},"fecha_actualizacion":null,"response_code":null,"response_description":null,"amount":null,"currency":null,"installment_number":null,"description":"Compra en Tienda de Nike","date_time":null,"ticket_number":null,"authorization_code":null,"commerce_name":null,"branch_name":null,"bin":null,"merchant_code":null,"payer_name":null,"payer_lastname":null,"card_last_numbers":null,"account_type":null,"created_at":null}]}
[30-Jul-2025 14:09:19 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 14:09:19 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 14:09:19 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 14:09:19 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 14:09:19 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 14:09:19 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 14:09:19 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SXLUG52104"}'
[30-Jul-2025 14:09:19 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SXLUG52104"}
[30-Jul-2025 14:09:19 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 14:09:19 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SXLUG52104
[30-Jul-2025 14:09:19 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/bancard/consultarEstado.php con datos: {"hookalias":"SXLUG52104"}
[30-Jul-2025 14:09:20 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 14:09:20 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[{"id":62,"hook_alias":"SXLUG52104","status":"pendiente","url":null,"fecha_creacion":{"date":"2025-07-30 09:07:50.013000","timezone_type":3,"timezone":"UTC"},"fecha_actualizacion":null,"response_code":null,"response_description":null,"amount":null,"currency":null,"installment_number":null,"description":"Compra en Tienda de Nike","date_time":null,"ticket_number":null,"authorization_code":null,"commerce_name":null,"branch_name":null,"bin":null,"merchant_code":null,"payer_name":null,"payer_lastname":null,"card_last_numbers":null,"account_type":null,"created_at":null}]}
[30-Jul-2025 14:09:24 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 14:09:24 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 14:09:24 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 14:09:24 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 14:09:24 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 14:09:24 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 14:09:24 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SXLUG52104"}'
[30-Jul-2025 14:09:24 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SXLUG52104"}
[30-Jul-2025 14:09:24 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 14:09:24 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SXLUG52104
[30-Jul-2025 14:09:24 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/bancard/consultarEstado.php con datos: {"hookalias":"SXLUG52104"}
[30-Jul-2025 14:09:25 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 14:09:25 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[{"id":62,"hook_alias":"SXLUG52104","status":"pendiente","url":null,"fecha_creacion":{"date":"2025-07-30 09:07:50.013000","timezone_type":3,"timezone":"UTC"},"fecha_actualizacion":null,"response_code":null,"response_description":null,"amount":null,"currency":null,"installment_number":null,"description":"Compra en Tienda de Nike","date_time":null,"ticket_number":null,"authorization_code":null,"commerce_name":null,"branch_name":null,"bin":null,"merchant_code":null,"payer_name":null,"payer_lastname":null,"card_last_numbers":null,"account_type":null,"created_at":null}]}
[30-Jul-2025 14:09:29 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 14:09:29 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 14:09:29 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 14:09:29 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 14:09:29 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 14:09:29 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 14:09:29 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SXLUG52104"}'
[30-Jul-2025 14:09:29 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SXLUG52104"}
[30-Jul-2025 14:09:29 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 14:09:29 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SXLUG52104
[30-Jul-2025 14:09:29 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/bancard/consultarEstado.php con datos: {"hookalias":"SXLUG52104"}
[30-Jul-2025 14:09:30 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 14:09:30 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[{"id":62,"hook_alias":"SXLUG52104","status":"pendiente","url":null,"fecha_creacion":{"date":"2025-07-30 09:07:50.013000","timezone_type":3,"timezone":"UTC"},"fecha_actualizacion":null,"response_code":null,"response_description":null,"amount":null,"currency":null,"installment_number":null,"description":"Compra en Tienda de Nike","date_time":null,"ticket_number":null,"authorization_code":null,"commerce_name":null,"branch_name":null,"bin":null,"merchant_code":null,"payer_name":null,"payer_lastname":null,"card_last_numbers":null,"account_type":null,"created_at":null}]}
[30-Jul-2025 14:09:35 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 14:09:35 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 14:09:35 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 14:09:35 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 14:09:35 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 14:09:35 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 14:09:35 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SXLUG52104"}'
[30-Jul-2025 14:09:35 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SXLUG52104"}
[30-Jul-2025 14:09:35 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 14:09:35 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SXLUG52104
[30-Jul-2025 14:09:35 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/bancard/consultarEstado.php con datos: {"hookalias":"SXLUG52104"}
[30-Jul-2025 14:09:35 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 14:09:35 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[{"id":62,"hook_alias":"SXLUG52104","status":"pendiente","url":null,"fecha_creacion":{"date":"2025-07-30 09:07:50.013000","timezone_type":3,"timezone":"UTC"},"fecha_actualizacion":null,"response_code":null,"response_description":null,"amount":null,"currency":null,"installment_number":null,"description":"Compra en Tienda de Nike","date_time":null,"ticket_number":null,"authorization_code":null,"commerce_name":null,"branch_name":null,"bin":null,"merchant_code":null,"payer_name":null,"payer_lastname":null,"card_last_numbers":null,"account_type":null,"created_at":null}]}
[30-Jul-2025 14:09:40 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 14:09:40 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 14:09:40 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 14:09:40 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 14:09:40 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 14:09:40 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 14:09:40 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SXLUG52104"}'
[30-Jul-2025 14:09:40 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SXLUG52104"}
[30-Jul-2025 14:09:40 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 14:09:40 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SXLUG52104
[30-Jul-2025 14:09:40 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/bancard/consultarEstado.php con datos: {"hookalias":"SXLUG52104"}
[30-Jul-2025 14:09:40 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 14:09:40 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[{"id":62,"hook_alias":"SXLUG52104","status":"pendiente","url":null,"fecha_creacion":{"date":"2025-07-30 09:07:50.013000","timezone_type":3,"timezone":"UTC"},"fecha_actualizacion":null,"response_code":null,"response_description":null,"amount":null,"currency":null,"installment_number":null,"description":"Compra en Tienda de Nike","date_time":null,"ticket_number":null,"authorization_code":null,"commerce_name":null,"branch_name":null,"bin":null,"merchant_code":null,"payer_name":null,"payer_lastname":null,"card_last_numbers":null,"account_type":null,"created_at":null}]}
[30-Jul-2025 14:09:45 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 14:09:45 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 14:09:45 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 14:09:45 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 14:09:45 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 14:09:45 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 14:09:45 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SXLUG52104"}'
[30-Jul-2025 14:09:45 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SXLUG52104"}
[30-Jul-2025 14:09:45 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 14:09:45 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SXLUG52104
[30-Jul-2025 14:09:45 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/bancard/consultarEstado.php con datos: {"hookalias":"SXLUG52104"}
[30-Jul-2025 14:09:45 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 14:09:45 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[{"id":62,"hook_alias":"SXLUG52104","status":"pendiente","url":null,"fecha_creacion":{"date":"2025-07-30 09:07:50.013000","timezone_type":3,"timezone":"UTC"},"fecha_actualizacion":null,"response_code":null,"response_description":null,"amount":null,"currency":null,"installment_number":null,"description":"Compra en Tienda de Nike","date_time":null,"ticket_number":null,"authorization_code":null,"commerce_name":null,"branch_name":null,"bin":null,"merchant_code":null,"payer_name":null,"payer_lastname":null,"card_last_numbers":null,"account_type":null,"created_at":null}]}
[30-Jul-2025 14:35:25 Europe/Berlin] [TEST CONNECTION INIT] Parámetros recibidos - TestPC: ************** | CodeEmp: 1 | CodSuc: 33
[30-Jul-2025 14:35:25 Europe/Berlin] [TEST CONNECTION DYNAMIC] TestPC no encontrado, construyendo dinámicamente | Servidor construido: **************.swoosh.net | Base de datos: SUC33 | Usuario: SA | Empresa: zavidoro | Dominio: swoosh.net
[30-Jul-2025 14:35:25 Europe/Berlin] [TEST CONNECTION] Tipo: Prueba Dinámica (TESTPC:**************, CODEMP:1, CODSUC:33) | Servidor: **************.swoosh.net | Base de datos: SUC33 | Usuario: SA | Timestamp: 2025-07-30 14:35:25
[30-Jul-2025 14:35:40 Europe/Berlin] [TEST CONNECTION ERROR] Prueba Dinámica (TESTPC:**************, CODEMP:1, CODSUC:33) | Servidor intentado: **************.swoosh.net | Base de datos: SUC33 | Error: SQLSTATE: 08001, Code: 53, Message: [Microsoft][ODBC Driver 17 for SQL Server]Named Pipes Provider: Could not open a connection to SQL Server [53]. ; SQLSTATE: HYT00, Code: 0, Message: [Microsoft][ODBC Driver 17 for SQL Server]Login timeout expired; SQLSTATE: 08001, Code: 53, Message: [Microsoft][ODBC Driver 17 for SQL Server]A network-related or instance-specific error has occurred while establishing a connection to SQL Server. Server is not found or not accessible. Check if instance name is correct and if SQL Server is configured to allow remote connections. For more information see SQL Server Books Online.
[30-Jul-2025 14:35:40 Europe/Berlin] Falló la conexión Prueba Dinámica (TESTPC:**************, CODEMP:1, CODSUC:33): SQLSTATE: 08001, Code: 53, Message: [Microsoft][ODBC Driver 17 for SQL Server]Named Pipes Provider: Could not open a connection to SQL Server [53]. ; SQLSTATE: HYT00, Code: 0, Message: [Microsoft][ODBC Driver 17 for SQL Server]Login timeout expired; SQLSTATE: 08001, Code: 53, Message: [Microsoft][ODBC Driver 17 for SQL Server]A network-related or instance-specific error has occurred while establishing a connection to SQL Server. Server is not found or not accessible. Check if instance name is correct and if SQL Server is configured to allow remote connections. For more information see SQL Server Books Online.
[30-Jul-2025 14:39:28 Europe/Berlin] [TEST CONNECTION INIT] Parámetros recibidos - TestPC: ************** | CodeEmp: 1 | CodSuc: 33
[30-Jul-2025 14:39:28 Europe/Berlin] [TEST CONNECTION DYNAMIC] TestPC no encontrado, construyendo dinámicamente | Servidor construido: **************.swoosh.net | Base de datos: SUC33 | Usuario: SA | Empresa: zavidoro | Dominio: swoosh.net
[30-Jul-2025 14:39:28 Europe/Berlin] [TEST CONNECTION] Tipo: Prueba Dinámica (TESTPC:**************, CODEMP:1, CODSUC:33) | Servidor: **************.swoosh.net | Base de datos: SUC33 | Usuario: SA | Timestamp: 2025-07-30 14:39:28
[30-Jul-2025 14:39:43 Europe/Berlin] [TEST CONNECTION ERROR] Prueba Dinámica (TESTPC:**************, CODEMP:1, CODSUC:33) | Servidor intentado: **************.swoosh.net | Base de datos: SUC33 | Error: SQLSTATE: 08001, Code: 53, Message: [Microsoft][ODBC Driver 17 for SQL Server]Named Pipes Provider: Could not open a connection to SQL Server [53]. ; SQLSTATE: HYT00, Code: 0, Message: [Microsoft][ODBC Driver 17 for SQL Server]Login timeout expired; SQLSTATE: 08001, Code: 53, Message: [Microsoft][ODBC Driver 17 for SQL Server]A network-related or instance-specific error has occurred while establishing a connection to SQL Server. Server is not found or not accessible. Check if instance name is correct and if SQL Server is configured to allow remote connections. For more information see SQL Server Books Online.
[30-Jul-2025 14:39:43 Europe/Berlin] Falló la conexión Prueba Dinámica (TESTPC:**************, CODEMP:1, CODSUC:33): SQLSTATE: 08001, Code: 53, Message: [Microsoft][ODBC Driver 17 for SQL Server]Named Pipes Provider: Could not open a connection to SQL Server [53]. ; SQLSTATE: HYT00, Code: 0, Message: [Microsoft][ODBC Driver 17 for SQL Server]Login timeout expired; SQLSTATE: 08001, Code: 53, Message: [Microsoft][ODBC Driver 17 for SQL Server]A network-related or instance-specific error has occurred while establishing a connection to SQL Server. Server is not found or not accessible. Check if instance name is correct and if SQL Server is configured to allow remote connections. For more information see SQL Server Books Online.
[30-Jul-2025 14:46:17 Europe/Berlin] [TEST CONNECTION INIT] Parámetros recibidos - TestPC: ************** | CodeEmp: 1 | CodSuc: 33
[30-Jul-2025 14:46:17 Europe/Berlin] [TEST CONNECTION DYNAMIC] TestPC no encontrado, construyendo dinámicamente | Servidor construido: **************.swoosh.net | Base de datos: SUC33 | Usuario: SA | Empresa: zavidoro | Dominio: swoosh.net
[30-Jul-2025 14:46:17 Europe/Berlin] [TEST CONNECTION] Tipo: Prueba Dinámica (TESTPC:**************, CODEMP:1, CODSUC:33) | Servidor: **************.swoosh.net | Base de datos: SUC33 | Usuario: SA | Timestamp: 2025-07-30 14:46:17
[30-Jul-2025 14:46:32 Europe/Berlin] [TEST CONNECTION ERROR] Prueba Dinámica (TESTPC:**************, CODEMP:1, CODSUC:33) | Servidor intentado: **************.swoosh.net | Base de datos: SUC33 | Error: SQLSTATE: 08001, Code: 53, Message: [Microsoft][ODBC Driver 17 for SQL Server]Named Pipes Provider: Could not open a connection to SQL Server [53]. ; SQLSTATE: HYT00, Code: 0, Message: [Microsoft][ODBC Driver 17 for SQL Server]Login timeout expired; SQLSTATE: 08001, Code: 53, Message: [Microsoft][ODBC Driver 17 for SQL Server]A network-related or instance-specific error has occurred while establishing a connection to SQL Server. Server is not found or not accessible. Check if instance name is correct and if SQL Server is configured to allow remote connections. For more information see SQL Server Books Online.
[30-Jul-2025 14:46:32 Europe/Berlin] Falló la conexión Prueba Dinámica (TESTPC:**************, CODEMP:1, CODSUC:33): SQLSTATE: 08001, Code: 53, Message: [Microsoft][ODBC Driver 17 for SQL Server]Named Pipes Provider: Could not open a connection to SQL Server [53]. ; SQLSTATE: HYT00, Code: 0, Message: [Microsoft][ODBC Driver 17 for SQL Server]Login timeout expired; SQLSTATE: 08001, Code: 53, Message: [Microsoft][ODBC Driver 17 for SQL Server]A network-related or instance-specific error has occurred while establishing a connection to SQL Server. Server is not found or not accessible. Check if instance name is correct and if SQL Server is configured to allow remote connections. For more information see SQL Server Books Online.
