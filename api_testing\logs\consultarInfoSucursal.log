[2025-07-10 03:19:32] Iniciando consulta de información de sucursal - Data: {"codemp":1,"codsuc":33,"query":"SELECT CODSUC, NOMBRE, DOMICILIO, DOMICILIO2, LOCALIDAD, TELEFONO FROM PVSucursales WHERE CODSUC = ?"}
[2025-07-10 03:19:32] Consulta de información de sucursal exitosa - Data: {"codemp":1,"codsuc":33,"results_count":1}
[2025-07-10 04:38:07] Iniciando consulta de información de sucursal - Data: {"codemp":1,"codsuc":33,"query":"SELECT CODSUC, RTRIM(NOMBRE) AS NOMBRE, RTRIM(DOMIC<PERSON>IO) AS DOMICILIO, RTRIM(DOMICILIO2) AS DOMICILIO2, RTRIM(LOCALIDAD) AS LOCALIDAD, RTRIM(TELEFONO) AS TELEFONO FROM PVSucursales WHERE CODSUC = ?"}
[2025-07-10 04:38:07] Consulta de información de sucursal exitosa - Data: {"codemp":1,"codsuc":33,"results_count":1}
[2025-07-14 22:15:23] Iniciando consulta de información de sucursal - Data: {"codemp":1,"codsuc":3,"test":"true","testPC":"MSALAS-M"}
[2025-07-14 22:15:23] Usando conexión de prueba - Data: {"test":"true","testPC":"MSALAS-M","codemp":1,"codsuc":3}
[2025-07-14 22:15:23] Excepción al ejecutar query de información de sucursal - Data: {"error":"Fall\u00f3 la conexi\u00f3n a la Base de Datos (Prueba Din\u00e1mica (TESTPC:MSALAS-M, CODEMP:1, CODSUC:3)): SQLSTATE: 28000, Code: 18456, Message: [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Login failed for user 'sa'.; SQLSTATE: 42000, Code: 4060, Message: [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Cannot open database \"SUC03\" requested by the login. The login failed.; SQLSTATE: 28000, Code: 18456, Message: [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Login failed for user 'sa'.; SQLSTATE: 42000, Code: 4060, Message: [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Cannot open database \"SUC03\" requested by the login. The login failed.","codemp":1,"codsuc":3,"query":"SELECT CODSUC, RTRIM(NOMBRE) AS NOMBRE, RTRIM(DOMICILIO) AS DOMICILIO, RTRIM(DOMICILIO2) AS DOMICILIO2, RTRIM(LOCALIDAD) AS LOCALIDAD, RTRIM(TELEFONO) AS TELEFONO FROM PVSucursales WHERE CODSUC = ?","test":"true","testPC":"MSALAS-M"}
[2025-07-14 22:15:26] Iniciando consulta de información de sucursal - Data: {"codemp":1,"codsuc":33,"test":"true","testPC":"MSALAS-M"}
[2025-07-14 22:15:26] Usando conexión de prueba - Data: {"test":"true","testPC":"MSALAS-M","codemp":1,"codsuc":33}
[2025-07-14 22:15:26] Consulta de información de sucursal exitosa - Data: {"codemp":1,"codsuc":33,"results_count":1,"test":"true","testPC":"MSALAS-M"}
[2025-07-14 22:15:33] Iniciando consulta de información de sucursal - Data: {"codemp":1,"codsuc":11,"test":"true","testPC":"MSALAS-M"}
[2025-07-14 22:15:33] Usando conexión de prueba - Data: {"test":"true","testPC":"MSALAS-M","codemp":1,"codsuc":11}
[2025-07-14 22:15:33] Consulta de información de sucursal exitosa - Data: {"codemp":1,"codsuc":11,"results_count":1,"test":"true","testPC":"MSALAS-M"}
[2025-07-14 22:15:45] Iniciando consulta de información de sucursal - Data: {"codemp":1,"codsuc":12,"test":"true","testPC":"MSALAS-M"}
[2025-07-14 22:15:45] Usando conexión de prueba - Data: {"test":"true","testPC":"MSALAS-M","codemp":1,"codsuc":12}
[2025-07-14 22:15:45] Excepción al ejecutar query de información de sucursal - Data: {"error":"Fall\u00f3 la conexi\u00f3n a la Base de Datos (Prueba Din\u00e1mica (TESTPC:MSALAS-M, CODEMP:1, CODSUC:12)): SQLSTATE: 28000, Code: 18456, Message: [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Login failed for user 'sa'.; SQLSTATE: 42000, Code: 4060, Message: [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Cannot open database \"SUC12\" requested by the login. The login failed.; SQLSTATE: 28000, Code: 18456, Message: [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Login failed for user 'sa'.; SQLSTATE: 42000, Code: 4060, Message: [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Cannot open database \"SUC12\" requested by the login. The login failed.","codemp":1,"codsuc":12,"query":"SELECT CODSUC, RTRIM(NOMBRE) AS NOMBRE, RTRIM(DOMICILIO) AS DOMICILIO, RTRIM(DOMICILIO2) AS DOMICILIO2, RTRIM(LOCALIDAD) AS LOCALIDAD, RTRIM(TELEFONO) AS TELEFONO FROM PVSucursales WHERE CODSUC = ?","test":"true","testPC":"MSALAS-M"}
[2025-07-14 22:15:56] Iniciando consulta de información de sucursal - Data: {"codemp":1,"codsuc":33,"test":"true","testPC":"MSALAS-M"}
[2025-07-14 22:15:56] Usando conexión de prueba - Data: {"test":"true","testPC":"MSALAS-M","codemp":1,"codsuc":33}
[2025-07-14 22:15:56] Consulta de información de sucursal exitosa - Data: {"codemp":1,"codsuc":33,"results_count":1,"test":"true","testPC":"MSALAS-M"}
[2025-07-14 22:31:43] Iniciando consulta de información de sucursal - Data: {"codemp":1,"codsuc":33,"test":"true","testPC":"MSALAS-M"}
[2025-07-14 22:31:43] Usando conexión de prueba - Data: {"test":"true","testPC":"MSALAS-M","codemp":1,"codsuc":33}
[2025-07-14 22:31:43] Consulta de información de sucursal exitosa - Data: {"codemp":1,"codsuc":33,"results_count":1,"test":"true","testPC":"MSALAS-M"}
[2025-07-14 22:31:47] Iniciando consulta de información de sucursal - Data: {"codemp":1,"codsuc":31,"test":"true","testPC":"MSALAS-M"}
[2025-07-14 22:31:47] Usando conexión de prueba - Data: {"test":"true","testPC":"MSALAS-M","codemp":1,"codsuc":31}
[2025-07-14 22:31:47] Excepción al ejecutar query de información de sucursal - Data: {"error":"Fall\u00f3 la conexi\u00f3n a la Base de Datos (Prueba Din\u00e1mica (TESTPC:MSALAS-M, CODEMP:1, CODSUC:31)): SQLSTATE: 28000, Code: 18456, Message: [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Login failed for user 'sa'.; SQLSTATE: 42000, Code: 4060, Message: [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Cannot open database \"SUC31\" requested by the login. The login failed.; SQLSTATE: 28000, Code: 18456, Message: [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Login failed for user 'sa'.; SQLSTATE: 42000, Code: 4060, Message: [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Cannot open database \"SUC31\" requested by the login. The login failed.","codemp":1,"codsuc":31,"query":"SELECT CODSUC, RTRIM(NOMBRE) AS NOMBRE, RTRIM(DOMICILIO) AS DOMICILIO, RTRIM(DOMICILIO2) AS DOMICILIO2, RTRIM(LOCALIDAD) AS LOCALIDAD, RTRIM(TELEFONO) AS TELEFONO FROM PVSucursales WHERE CODSUC = ?","test":"true","testPC":"MSALAS-M"}
[2025-07-14 22:32:49] Iniciando consulta de información de sucursal - Data: {"codemp":1,"codsuc":33,"test":"true","testPC":"MSALAS-M"}
[2025-07-14 22:32:49] Usando conexión de prueba - Data: {"test":"true","testPC":"MSALAS-M","codemp":1,"codsuc":33}
[2025-07-14 22:32:49] Consulta de información de sucursal exitosa - Data: {"codemp":1,"codsuc":33,"results_count":1,"test":"true","testPC":"MSALAS-M"}
[2025-07-14 22:33:32] Iniciando consulta de información de sucursal - Data: {"codemp":1,"codsuc":11,"test":"true","testPC":"MSALAS-M"}
[2025-07-14 22:33:32] Usando conexión de prueba - Data: {"test":"true","testPC":"MSALAS-M","codemp":1,"codsuc":11}
[2025-07-14 22:33:32] Consulta de información de sucursal exitosa - Data: {"codemp":1,"codsuc":11,"results_count":1,"test":"true","testPC":"MSALAS-M"}
[2025-07-21 03:19:10] Iniciando consulta de información de sucursal - Data: {"codemp":1,"codsuc":33,"test":"true","testPC":"MSALAS-M"}
[2025-07-21 03:19:10] Usando conexión de prueba - Data: {"test":"true","testPC":"MSALAS-M","codemp":1,"codsuc":33}
[2025-07-21 03:19:10] Consulta de información de sucursal exitosa - Data: {"codemp":1,"codsuc":33,"results_count":1,"test":"true","testPC":"MSALAS-M"}
[2025-07-21 03:32:54] Iniciando consulta de información de sucursal - Data: {"codemp":1,"codsuc":33,"test":"true","testPC":"MSALAS-M"}
[2025-07-21 03:32:54] Usando conexión de prueba - Data: {"test":"true","testPC":"MSALAS-M","codemp":1,"codsuc":33}
[2025-07-21 03:32:54] Consulta de información de sucursal exitosa - Data: {"codemp":1,"codsuc":33,"results_count":1,"test":"true","testPC":"MSALAS-M"}
[2025-07-21 03:36:03] Iniciando consulta de información de sucursal - Data: {"codemp":1,"codsuc":33,"test":null,"testPC":null}
[2025-07-21 03:36:28] Iniciando consulta de información de sucursal - Data: {"codemp":1,"codsuc":33,"test":"true","testPC":"MSALAS-M"}
[2025-07-21 03:36:28] Usando conexión de prueba - Data: {"test":"true","testPC":"MSALAS-M","codemp":1,"codsuc":33}
[2025-07-21 03:36:28] Consulta de información de sucursal exitosa - Data: {"codemp":1,"codsuc":33,"results_count":1,"test":"true","testPC":"MSALAS-M"}
[2025-07-21 03:36:48] Excepción al ejecutar query de información de sucursal - Data: {"error":"Fall\u00f3 la conexi\u00f3n a la Base de Datos (Sucursal (CODEMP:1, CODSUC:33)): SQLSTATE: 08001, Code: 64, Message: [Microsoft][ODBC Driver 17 for SQL Server]Named Pipes Provider: Could not open a connection to SQL Server [64]. ; SQLSTATE: HYT00, Code: 0, Message: [Microsoft][ODBC Driver 17 for SQL Server]Login timeout expired; SQLSTATE: 08001, Code: 64, Message: [Microsoft][ODBC Driver 17 for SQL Server]A network-related or instance-specific error has occurred while establishing a connection to SQL Server. Server is not found or not accessible. Check if instance name is correct and if SQL Server is configured to allow remote connections. For more information see SQL Server Books Online.","codemp":1,"codsuc":33,"query":"SELECT CODSUC, RTRIM(NOMBRE) AS NOMBRE, RTRIM(DOMICILIO) AS DOMICILIO, RTRIM(DOMICILIO2) AS DOMICILIO2, RTRIM(LOCALIDAD) AS LOCALIDAD, RTRIM(TELEFONO) AS TELEFONO FROM PVSucursales WHERE CODSUC = ?","test":null,"testPC":null}
[2025-07-21 03:37:12] Iniciando consulta de información de sucursal - Data: {"codemp":1,"codsuc":33,"test":"true","testPC":"MSALAS-M"}
[2025-07-21 03:37:12] Usando conexión de prueba - Data: {"test":"true","testPC":"MSALAS-M","codemp":1,"codsuc":33}
[2025-07-21 03:37:12] Consulta de información de sucursal exitosa - Data: {"codemp":1,"codsuc":33,"results_count":1,"test":"true","testPC":"MSALAS-M"}
[2025-07-21 15:01:12] Iniciando consulta de información de sucursal - Data: {"codemp":1,"codsuc":33,"test":"true","testPC":"MSALAS-B"}
[2025-07-21 15:01:12] Usando conexión de prueba - Data: {"test":"true","testPC":"MSALAS-B","codemp":1,"codsuc":33}
[2025-07-21 15:01:27] Excepción al ejecutar query de información de sucursal - Data: {"error":"Fall\u00f3 la conexi\u00f3n a la Base de Datos (Prueba Din\u00e1mica (TESTPC:MSALAS-B, CODEMP:1, CODSUC:33)): SQLSTATE: 08001, Code: 53, Message: [Microsoft][ODBC Driver 17 for SQL Server]Named Pipes Provider: Could not open a connection to SQL Server [53]. ; SQLSTATE: HYT00, Code: 0, Message: [Microsoft][ODBC Driver 17 for SQL Server]Login timeout expired; SQLSTATE: 08001, Code: 53, Message: [Microsoft][ODBC Driver 17 for SQL Server]A network-related or instance-specific error has occurred while establishing a connection to SQL Server. Server is not found or not accessible. Check if instance name is correct and if SQL Server is configured to allow remote connections. For more information see SQL Server Books Online.","codemp":1,"codsuc":33,"query":"SELECT CODSUC, RTRIM(NOMBRE) AS NOMBRE, RTRIM(DOMICILIO) AS DOMICILIO, RTRIM(DOMICILIO2) AS DOMICILIO2, RTRIM(LOCALIDAD) AS LOCALIDAD, RTRIM(TELEFONO) AS TELEFONO FROM PVSucursales WHERE CODSUC = ?","test":"true","testPC":"MSALAS-B"}
[2025-07-28 14:46:14] Iniciando consulta de información de sucursal - Data: {"codemp":1,"codsuc":33,"test":"true","testPC":"LOCALHOST"}
[2025-07-28 14:46:14] Usando conexión de prueba - Data: {"test":"true","testPC":"LOCALHOST","codemp":1,"codsuc":33}
[2025-07-28 14:46:30] Excepción al ejecutar query de información de sucursal - Data: {"error":"Fall\u00f3 la conexi\u00f3n a la Base de Datos (Prueba Espec\u00edfica (TESTPC:LOCALHOST, CODEMP:1, CODSUC:33)): SQLSTATE: 08001, Code: 2, Message: [Microsoft][ODBC Driver 17 for SQL Server]Named Pipes Provider: Could not open a connection to SQL Server [2]. ; SQLSTATE: HYT00, Code: 0, Message: [Microsoft][ODBC Driver 17 for SQL Server]Login timeout expired; SQLSTATE: 08001, Code: 2, Message: [Microsoft][ODBC Driver 17 for SQL Server]A network-related or instance-specific error has occurred while establishing a connection to SQL Server. Server is not found or not accessible. Check if instance name is correct and if SQL Server is configured to allow remote connections. For more information see SQL Server Books Online.","codemp":1,"codsuc":33,"query":"SELECT CODSUC, RTRIM(NOMBRE) AS NOMBRE, RTRIM(DOMICILIO) AS DOMICILIO, RTRIM(DOMICILIO2) AS DOMICILIO2, RTRIM(LOCALIDAD) AS LOCALIDAD, RTRIM(TELEFONO) AS TELEFONO FROM PVSucursales WHERE CODSUC = ?","test":"true","testPC":"LOCALHOST"}
[2025-07-28 14:47:58] Iniciando consulta de información de sucursal - Data: {"codemp":1,"codsuc":33,"test":"true","testPC":"LOCALHOST"}
[2025-07-28 14:47:58] Usando conexión de prueba - Data: {"test":"true","testPC":"LOCALHOST","codemp":1,"codsuc":33}
[2025-07-28 14:47:58] Consulta de información de sucursal exitosa - Data: {"codemp":1,"codsuc":33,"results_count":1,"test":"true","testPC":"LOCALHOST"}
[2025-07-28 14:49:32] Iniciando consulta de información de sucursal - Data: {"codemp":1,"codsuc":33,"test":"true","testPC":"LOCALHOST"}
[2025-07-28 14:49:32] Usando conexión de prueba - Data: {"test":"true","testPC":"LOCALHOST","codemp":1,"codsuc":33}
[2025-07-28 14:49:32] Consulta de información de sucursal exitosa - Data: {"codemp":1,"codsuc":33,"results_count":1,"test":"true","testPC":"LOCALHOST"}
[2025-07-30 14:59:44] Iniciando consulta de información de sucursal - Data: {"codemp":1,"codsuc":33,"test":"true","testPC":"**************"}
[2025-07-30 14:59:44] Usando conexión de prueba - Data: {"test":"true","testPC":"**************","codemp":1,"codsuc":33}
[2025-07-30 14:59:44] Consulta de información de sucursal exitosa - Data: {"codemp":1,"codsuc":33,"results_count":1,"test":"true","testPC":"**************"}
[2025-07-30 15:51:53] Iniciando consulta de información de sucursal - Data: {"codemp":1,"codsuc":33,"test":"true","testPC":"**************"}
[2025-07-30 15:51:53] Usando conexión de prueba - Data: {"test":"true","testPC":"**************","codemp":1,"codsuc":33}
[2025-07-30 15:51:53] Consulta de información de sucursal exitosa - Data: {"codemp":1,"codsuc":33,"results_count":1,"test":"true","testPC":"**************"}
[2025-07-30 15:59:30] Iniciando consulta de información de sucursal - Data: {"codemp":1,"codsuc":33,"test":"true","testPC":"**************"}
[2025-07-30 15:59:30] Usando conexión de prueba - Data: {"test":"true","testPC":"**************","codemp":1,"codsuc":33}
[2025-07-30 15:59:30] Consulta de información de sucursal exitosa - Data: {"codemp":1,"codsuc":33,"results_count":1,"test":"true","testPC":"**************"}
[2025-07-30 19:20:22] Iniciando consulta de información de sucursal - Data: {"codemp":1,"codsuc":33,"test":"true","testPC":"**************"}
[2025-07-30 19:20:22] Iniciando consulta de información de sucursal - Data: {"codemp":1,"codsuc":33,"test":"true","testPC":"**************"}
[2025-07-30 19:20:22] Usando conexión de prueba - Data: {"test":"true","testPC":"**************","codemp":1,"codsuc":33}
[2025-07-30 19:20:22] Consulta de información de sucursal exitosa - Data: {"codemp":1,"codsuc":33,"results_count":1,"test":"true","testPC":"**************"}
[2025-07-30 19:20:22] Consulta de información de sucursal exitosa - Data: {"codemp":1,"codsuc":33,"results_count":1,"test":"true","testPC":"**************"}
[2025-07-30 19:31:35] Iniciando consulta de información de sucursal - Data: {"codemp":1,"codsuc":33,"test":"true","testPC":"**************"}
[2025-07-30 19:31:35] Usando conexión de prueba - Data: {"test":"true","testPC":"**************","codemp":1,"codsuc":33}
[2025-07-30 19:31:35] Consulta de información de sucursal exitosa - Data: {"codemp":1,"codsuc":33,"results_count":1,"test":"true","testPC":"**************"}
[2025-07-30 19:40:09] Iniciando consulta de información de sucursal - Data: {"codemp":1,"codsuc":33,"test":"true","testPC":"**************"}
[2025-07-30 19:40:09] Usando conexión de prueba - Data: {"test":"true","testPC":"**************","codemp":1,"codsuc":33}
[2025-07-30 19:40:09] Consulta de información de sucursal exitosa - Data: {"codemp":1,"codsuc":33,"results_count":1,"test":"true","testPC":"**************"}
[2025-07-30 20:02:45] Iniciando consulta de información de sucursal - Data: {"codemp":1,"codsuc":33,"test":"true","testPC":"**************"}
[2025-07-30 20:02:45] Usando conexión de prueba - Data: {"test":"true","testPC":"**************","codemp":1,"codsuc":33}
[2025-07-30 20:02:45] Consulta de información de sucursal exitosa - Data: {"codemp":1,"codsuc":33,"results_count":1,"test":"true","testPC":"**************"}
[2025-07-30 20:03:09] Iniciando consulta de información de sucursal - Data: {"codemp":1,"codsuc":33,"test":"true","testPC":"**************"}
[2025-07-30 20:03:09] Usando conexión de prueba - Data: {"test":"true","testPC":"**************","codemp":1,"codsuc":33}
[2025-07-30 20:03:09] Consulta de información de sucursal exitosa - Data: {"codemp":1,"codsuc":33,"results_count":1,"test":"true","testPC":"**************"}
