<?php

error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/php_errors.log');

header('Content-Type: application/json');

$aliasEnvironment = "prod";

function makeHttpRequest($url, $data) {
    $ch = curl_init();

    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Content-Length: ' . strlen(json_encode($data))
    ]);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);

    curl_close($ch);

    if ($error) {
        throw new Exception("Error en cURL: " . $error);
    }

    return [
        'response' => $response,
        'http_code' => $httpCode
    ];
}

// Log detallado para debugging
error_log("registrarAlias.php - Método HTTP: " . $_SERVER['REQUEST_METHOD']);
error_log("registrarAlias.php - Content-Type: " . (isset($_SERVER['CONTENT_TYPE']) ? $_SERVER['CONTENT_TYPE'] : 'No definido'));
error_log("registrarAlias.php - Query String: " . (isset($_SERVER['QUERY_STRING']) ? $_SERVER['QUERY_STRING'] : 'Vacío'));
error_log("registrarAlias.php - GET params: " . json_encode($_GET));
error_log("registrarAlias.php - POST params: " . json_encode($_POST));

$input = file_get_contents('php://input');
$data = json_decode($input, true);

// Log para debugging - ver qué datos están llegando
error_log("registrarAlias.php - Input raw length: " . strlen($input));
error_log("registrarAlias.php - Input raw: '" . $input . "'");
error_log("registrarAlias.php - JSON decode result: " . json_encode($data));
error_log("registrarAlias.php - JSON last error: " . json_last_error_msg());

// Inicializar $data si es null
if ($data === null) {
    $data = array();
}

// Buscar parámetros requeridos en diferentes fuentes
$requiredParams = ['hookalias', 'amount', 'description', 'created_at'];
$missingParams = [];

foreach ($requiredParams as $param) {
    if (!isset($data[$param])) {
        // Buscar en POST params
        if (isset($_POST[$param])) {
            $data[$param] = $_POST[$param];
            error_log("registrarAlias.php - $param encontrado en POST: " . $_POST[$param]);
        }
        // Buscar en GET params
        elseif (isset($_GET[$param])) {
            $data[$param] = $_GET[$param];
            error_log("registrarAlias.php - $param encontrado en GET: " . $_GET[$param]);
        }
        else {
            $missingParams[] = $param;
        }
    } else {
        error_log("registrarAlias.php - $param encontrado en JSON body: " . $data[$param]);
    }
}

// Si faltan parámetros requeridos
if (!empty($missingParams)) {
    error_log("registrarAlias.php - ERROR: Parámetros faltantes: " . implode(', ', $missingParams));
    http_response_code(400);
    echo json_encode([
        'status' => 'error',
        'message' => 'Parámetros requeridos no proporcionados: ' . implode(', ', $missingParams),
        'debug_info' => [
            'method' => $_SERVER['REQUEST_METHOD'],
            'content_type' => isset($_SERVER['CONTENT_TYPE']) ? $_SERVER['CONTENT_TYPE'] : null,
            'json_data' => $data,
            'post_params' => $_POST,
            'get_params' => $_GET,
            'raw_input_length' => strlen($input),
            'missing_params' => $missingParams
        ]
    ]);
    exit;
}

// if ($aliasEnvironment === "dev") {
//     $targetUrl = "https://zavidoro.com.py/testing/registrarAlias.php";
// } else {
//     $targetUrl = "https://zavidoro.com.py/bancard/registrarAlias.php";
// }

$targetUrl = "https://zavidoro.com.py/bancard/registrarAlias.php";
error_log("registrarAlias.php - Enviando a: " . $targetUrl . " con datos: " . json_encode($data));

try {
    $result = makeHttpRequest($targetUrl, $data);

    error_log("registrarAlias.php - Respuesta del servidor remoto - HTTP Code: " . $result['http_code']);
    error_log("registrarAlias.php - Respuesta del servidor remoto - Body: " . $result['response']);

    http_response_code($result['http_code']);
    echo $result['response'];

} catch (Exception $e) {
    error_log("registrarAlias.php - Error en makeHttpRequest: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => 'Error al procesar la solicitud: ' . $e->getMessage()
    ]);
}
?>
