[30-Jul-2025 14:59:44 Europe/Berlin] [TEST CONNECTION INIT] Parámetros recibidos - TestPC: ************** | CodeEmp: 1 | CodSuc: 33
[30-Jul-2025 14:59:44 Europe/Berlin] [TEST CONNECTION IP] Conectando a IP directa | Servidor: ************** | Base de datos: SUC33 | Usuario: SA | Empresa: zavidoro
[30-Jul-2025 14:59:44 Europe/Berlin] [TEST CONNECTION] Tipo: Prueba IP (TESTPC:**************, CODEMP:1, CODSUC:33) | Servidor: ************** | Base de datos: SUC33 | Usuario: SA | Timestamp: 2025-07-30 14:59:44
[30-Jul-2025 14:59:44 Europe/Berlin] [TEST CONNECTION SUCCESS] Prueba IP (TESTPC:**************, CODEMP:1, CODSUC:33) | Servidor: ************** | Base de datos: SUC33 | Conexión establecida exitosamente
[30-Jul-2025 15:00:26 Europe/Berlin] registrarAlias.php - Método HTTP: PUT
[30-Jul-2025 15:00:26 Europe/Berlin] registrarAlias.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 15:00:26 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 15:00:26 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 15:00:26 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 15:00:26 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 15:00:26 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 15:00:26 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 15:00:26 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SKRCP35062"}'
[30-Jul-2025 15:00:26 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SKRCP35062"}
[30-Jul-2025 15:00:26 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 15:00:26 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SKRCP35062
[30-Jul-2025 15:00:26 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/testing/consultarEstado.php con datos: {"hookalias":"SKRCP35062"}
[30-Jul-2025 15:00:26 Europe/Berlin] registrarAlias.php - Query String: 
[30-Jul-2025 15:00:27 Europe/Berlin] registrarAlias.php - GET params: []
[30-Jul-2025 15:00:27 Europe/Berlin] registrarAlias.php - POST params: []
[30-Jul-2025 15:00:27 Europe/Berlin] registrarAlias.php - Input raw length: 119
[30-Jul-2025 15:00:27 Europe/Berlin] registrarAlias.php - Input raw: '{"hookalias":"SKRCP35062","amount":66510,"description":"Compra en Tienda de Nike","created_at":"30\/07\/2025 10:00:24"}'
[30-Jul-2025 15:00:27 Europe/Berlin] registrarAlias.php - JSON decode result: {"hookalias":"SKRCP35062","amount":66510,"description":"Compra en Tienda de Nike","created_at":"30\/07\/2025 10:00:24"}
[30-Jul-2025 15:00:27 Europe/Berlin] registrarAlias.php - JSON last error: No error
[30-Jul-2025 15:00:27 Europe/Berlin] registrarAlias.php - hookalias encontrado en JSON body: SKRCP35062
[30-Jul-2025 15:00:27 Europe/Berlin] registrarAlias.php - amount encontrado en JSON body: 66510
[30-Jul-2025 15:00:27 Europe/Berlin] registrarAlias.php - description encontrado en JSON body: Compra en Tienda de Nike
[30-Jul-2025 15:00:27 Europe/Berlin] registrarAlias.php - created_at encontrado en JSON body: 30/07/2025 10:00:24
[30-Jul-2025 15:00:27 Europe/Berlin] registrarAlias.php - Enviando a: https://zavidoro.com.py/bancard/registrarAlias.php con datos: {"hookalias":"SKRCP35062","amount":66510,"description":"Compra en Tienda de Nike","created_at":"30\/07\/2025 10:00:24"}
[30-Jul-2025 15:00:28 Europe/Berlin] registrarAlias.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 15:00:28 Europe/Berlin] registrarAlias.php - Respuesta del servidor remoto - Body: {"status":"success","message":"Alias registrado correctamente"}
[30-Jul-2025 15:00:28 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 15:00:28 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[]}
[30-Jul-2025 15:00:31 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 15:00:31 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 15:00:31 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 15:00:31 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 15:00:31 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 15:00:31 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 15:00:31 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SKRCP35062"}'
[30-Jul-2025 15:00:31 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SKRCP35062"}
[30-Jul-2025 15:00:31 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 15:00:31 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SKRCP35062
[30-Jul-2025 15:00:31 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/testing/consultarEstado.php con datos: {"hookalias":"SKRCP35062"}
[30-Jul-2025 15:00:32 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 15:00:32 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[]}
[30-Jul-2025 15:00:36 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 15:00:36 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 15:00:36 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 15:00:36 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 15:00:36 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 15:00:36 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 15:00:36 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SKRCP35062"}'
[30-Jul-2025 15:00:36 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SKRCP35062"}
[30-Jul-2025 15:00:36 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 15:00:36 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SKRCP35062
[30-Jul-2025 15:00:36 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/testing/consultarEstado.php con datos: {"hookalias":"SKRCP35062"}
[30-Jul-2025 15:00:37 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 15:00:37 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[]}
[30-Jul-2025 15:00:41 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 15:00:41 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 15:00:41 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 15:00:41 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 15:00:41 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 15:00:41 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 15:00:41 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SKRCP35062"}'
[30-Jul-2025 15:00:41 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SKRCP35062"}
[30-Jul-2025 15:00:41 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 15:00:41 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SKRCP35062
[30-Jul-2025 15:00:41 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/testing/consultarEstado.php con datos: {"hookalias":"SKRCP35062"}
[30-Jul-2025 15:00:42 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 15:00:42 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[]}
[30-Jul-2025 15:00:46 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 15:00:46 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 15:00:46 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 15:00:46 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 15:00:46 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 15:00:46 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 15:00:46 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SKRCP35062"}'
[30-Jul-2025 15:00:46 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SKRCP35062"}
[30-Jul-2025 15:00:46 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 15:00:46 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SKRCP35062
[30-Jul-2025 15:00:46 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/testing/consultarEstado.php con datos: {"hookalias":"SKRCP35062"}
[30-Jul-2025 15:00:47 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 15:00:47 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[]}
[30-Jul-2025 15:00:51 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 15:00:51 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 15:00:51 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 15:00:51 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 15:00:51 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 15:00:51 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 15:00:51 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SKRCP35062"}'
[30-Jul-2025 15:00:51 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SKRCP35062"}
[30-Jul-2025 15:00:51 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 15:00:51 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SKRCP35062
[30-Jul-2025 15:00:51 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/testing/consultarEstado.php con datos: {"hookalias":"SKRCP35062"}
[30-Jul-2025 15:00:52 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 15:00:52 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[]}
[30-Jul-2025 15:00:56 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 15:00:56 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 15:00:56 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 15:00:56 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 15:00:56 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 15:00:56 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 15:00:56 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SKRCP35062"}'
[30-Jul-2025 15:00:56 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SKRCP35062"}
[30-Jul-2025 15:00:56 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 15:00:56 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SKRCP35062
[30-Jul-2025 15:00:56 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/testing/consultarEstado.php con datos: {"hookalias":"SKRCP35062"}
[30-Jul-2025 15:00:57 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 15:00:57 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[]}
[30-Jul-2025 15:01:01 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 15:01:01 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 15:01:01 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 15:01:01 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 15:01:01 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 15:01:01 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 15:01:01 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SKRCP35062"}'
[30-Jul-2025 15:01:01 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SKRCP35062"}
[30-Jul-2025 15:01:01 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 15:01:01 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SKRCP35062
[30-Jul-2025 15:01:01 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/testing/consultarEstado.php con datos: {"hookalias":"SKRCP35062"}
[30-Jul-2025 15:01:02 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 15:01:02 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[]}
[30-Jul-2025 15:01:07 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 15:01:07 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 15:01:07 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 15:01:07 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 15:01:07 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 15:01:07 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 15:01:07 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SKRCP35062"}'
[30-Jul-2025 15:01:07 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SKRCP35062"}
[30-Jul-2025 15:01:07 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 15:01:07 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SKRCP35062
[30-Jul-2025 15:01:07 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/testing/consultarEstado.php con datos: {"hookalias":"SKRCP35062"}
[30-Jul-2025 15:01:07 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 15:01:07 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[]}
[30-Jul-2025 15:01:11 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 15:01:11 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 15:01:11 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 15:01:11 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 15:01:11 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 15:01:11 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 15:01:11 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SKRCP35062"}'
[30-Jul-2025 15:01:11 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SKRCP35062"}
[30-Jul-2025 15:01:11 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 15:01:11 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SKRCP35062
[30-Jul-2025 15:01:12 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/testing/consultarEstado.php con datos: {"hookalias":"SKRCP35062"}
[30-Jul-2025 15:01:12 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 15:01:12 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[]}
[30-Jul-2025 15:01:19 Europe/Berlin] registrarAlias.php - Método HTTP: PUT
[30-Jul-2025 15:01:19 Europe/Berlin] registrarAlias.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 15:01:19 Europe/Berlin] registrarAlias.php - Query String: 
[30-Jul-2025 15:01:19 Europe/Berlin] registrarAlias.php - GET params: []
[30-Jul-2025 15:01:19 Europe/Berlin] registrarAlias.php - POST params: []
[30-Jul-2025 15:01:19 Europe/Berlin] registrarAlias.php - Input raw length: 119
[30-Jul-2025 15:01:19 Europe/Berlin] registrarAlias.php - Input raw: '{"hookalias":"STFDY19208","amount":66510,"description":"Compra en Tienda de Nike","created_at":"30\/07\/2025 10:01:17"}'
[30-Jul-2025 15:01:19 Europe/Berlin] registrarAlias.php - JSON decode result: {"hookalias":"STFDY19208","amount":66510,"description":"Compra en Tienda de Nike","created_at":"30\/07\/2025 10:01:17"}
[30-Jul-2025 15:01:19 Europe/Berlin] registrarAlias.php - JSON last error: No error
[30-Jul-2025 15:01:19 Europe/Berlin] registrarAlias.php - hookalias encontrado en JSON body: STFDY19208
[30-Jul-2025 15:01:19 Europe/Berlin] registrarAlias.php - amount encontrado en JSON body: 66510
[30-Jul-2025 15:01:19 Europe/Berlin] registrarAlias.php - description encontrado en JSON body: Compra en Tienda de Nike
[30-Jul-2025 15:01:19 Europe/Berlin] registrarAlias.php - created_at encontrado en JSON body: 30/07/2025 10:01:17
[30-Jul-2025 15:01:19 Europe/Berlin] registrarAlias.php - Enviando a: https://zavidoro.com.py/bancard/registrarAlias.php con datos: {"hookalias":"STFDY19208","amount":66510,"description":"Compra en Tienda de Nike","created_at":"30\/07\/2025 10:01:17"}
[30-Jul-2025 15:01:19 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 15:01:19 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 15:01:19 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 15:01:19 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 15:01:19 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 15:01:19 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 15:01:19 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"STFDY19208"}'
[30-Jul-2025 15:01:19 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"STFDY19208"}
[30-Jul-2025 15:01:19 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 15:01:19 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: STFDY19208
[30-Jul-2025 15:01:19 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/testing/consultarEstado.php con datos: {"hookalias":"STFDY19208"}
[30-Jul-2025 15:01:19 Europe/Berlin] registrarAlias.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 15:01:19 Europe/Berlin] registrarAlias.php - Respuesta del servidor remoto - Body: {"status":"success","message":"Alias registrado correctamente"}
[30-Jul-2025 15:01:20 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 15:01:20 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[]}
[30-Jul-2025 15:01:24 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 15:01:24 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 15:01:24 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 15:01:24 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 15:01:24 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 15:01:24 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 15:01:24 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"STFDY19208"}'
[30-Jul-2025 15:01:24 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"STFDY19208"}
[30-Jul-2025 15:01:24 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 15:01:24 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: STFDY19208
[30-Jul-2025 15:01:24 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/testing/consultarEstado.php con datos: {"hookalias":"STFDY19208"}
[30-Jul-2025 15:01:24 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 15:01:24 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[]}
[30-Jul-2025 15:01:29 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 15:01:29 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 15:01:29 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 15:01:29 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 15:01:29 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 15:01:29 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 15:01:29 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"STFDY19208"}'
[30-Jul-2025 15:01:29 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"STFDY19208"}
[30-Jul-2025 15:01:29 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 15:01:29 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: STFDY19208
[30-Jul-2025 15:01:29 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/testing/consultarEstado.php con datos: {"hookalias":"STFDY19208"}
[30-Jul-2025 15:01:29 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 15:01:29 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[]}
[30-Jul-2025 15:01:34 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 15:01:34 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 15:01:34 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 15:01:34 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 15:01:34 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 15:01:34 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 15:01:34 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"STFDY19208"}'
[30-Jul-2025 15:01:34 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"STFDY19208"}
[30-Jul-2025 15:01:34 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 15:01:34 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: STFDY19208
[30-Jul-2025 15:01:34 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/testing/consultarEstado.php con datos: {"hookalias":"STFDY19208"}
[30-Jul-2025 15:01:34 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 15:01:34 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[]}
[30-Jul-2025 15:01:39 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 15:01:39 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 15:01:39 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 15:01:39 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 15:01:39 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 15:01:39 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 15:01:39 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"STFDY19208"}'
[30-Jul-2025 15:01:39 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"STFDY19208"}
[30-Jul-2025 15:01:39 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 15:01:39 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: STFDY19208
[30-Jul-2025 15:01:39 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/testing/consultarEstado.php con datos: {"hookalias":"STFDY19208"}
[30-Jul-2025 15:01:39 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 15:01:39 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[]}
[30-Jul-2025 15:01:44 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 15:01:44 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 15:01:44 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 15:01:44 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 15:01:44 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 15:01:44 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 15:01:44 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"STFDY19208"}'
[30-Jul-2025 15:01:44 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"STFDY19208"}
[30-Jul-2025 15:01:44 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 15:01:44 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: STFDY19208
[30-Jul-2025 15:01:44 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/testing/consultarEstado.php con datos: {"hookalias":"STFDY19208"}
[30-Jul-2025 15:01:44 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 15:01:44 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[]}
[30-Jul-2025 15:01:49 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 15:01:49 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 15:01:49 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 15:01:49 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 15:01:49 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 15:01:49 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 15:01:49 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"STFDY19208"}'
[30-Jul-2025 15:01:49 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"STFDY19208"}
[30-Jul-2025 15:01:49 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 15:01:49 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: STFDY19208
[30-Jul-2025 15:01:49 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/testing/consultarEstado.php con datos: {"hookalias":"STFDY19208"}
[30-Jul-2025 15:01:49 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 15:01:49 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[]}
[30-Jul-2025 15:01:54 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 15:01:54 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 15:01:54 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 15:01:54 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 15:01:54 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 15:01:54 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 15:01:54 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"STFDY19208"}'
[30-Jul-2025 15:01:54 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"STFDY19208"}
[30-Jul-2025 15:01:54 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 15:01:54 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: STFDY19208
[30-Jul-2025 15:01:54 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/testing/consultarEstado.php con datos: {"hookalias":"STFDY19208"}
[30-Jul-2025 15:01:55 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 15:01:55 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[]}
[30-Jul-2025 15:01:59 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 15:01:59 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 15:01:59 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 15:01:59 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 15:01:59 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 15:01:59 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 15:01:59 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"STFDY19208"}'
[30-Jul-2025 15:01:59 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"STFDY19208"}
[30-Jul-2025 15:01:59 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 15:01:59 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: STFDY19208
[30-Jul-2025 15:01:59 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/testing/consultarEstado.php con datos: {"hookalias":"STFDY19208"}
[30-Jul-2025 15:01:59 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 15:01:59 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[]}
[30-Jul-2025 15:02:04 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 15:02:04 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 15:02:04 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 15:02:04 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 15:02:04 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 15:02:04 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 15:02:04 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"STFDY19208"}'
[30-Jul-2025 15:02:04 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"STFDY19208"}
[30-Jul-2025 15:02:04 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 15:02:04 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: STFDY19208
[30-Jul-2025 15:02:04 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/testing/consultarEstado.php con datos: {"hookalias":"STFDY19208"}
[30-Jul-2025 15:02:04 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 15:02:04 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[]}
[30-Jul-2025 15:02:09 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 15:02:09 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 15:02:09 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 15:02:09 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 15:02:09 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 15:02:09 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 15:02:09 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"STFDY19208"}'
[30-Jul-2025 15:02:09 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"STFDY19208"}
[30-Jul-2025 15:02:09 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 15:02:09 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: STFDY19208
[30-Jul-2025 15:02:09 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/testing/consultarEstado.php con datos: {"hookalias":"STFDY19208"}
[30-Jul-2025 15:02:09 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 15:02:09 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[]}
[30-Jul-2025 15:02:14 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 15:02:14 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 15:02:14 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 15:02:14 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 15:02:14 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 15:02:14 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 15:02:14 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"STFDY19208"}'
[30-Jul-2025 15:02:14 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"STFDY19208"}
[30-Jul-2025 15:02:14 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 15:02:14 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: STFDY19208
[30-Jul-2025 15:02:14 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/testing/consultarEstado.php con datos: {"hookalias":"STFDY19208"}
[30-Jul-2025 15:02:15 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 15:02:15 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[]}
[30-Jul-2025 15:02:19 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 15:02:19 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 15:02:19 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 15:02:19 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 15:02:19 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 15:02:19 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 15:02:19 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"STFDY19208"}'
[30-Jul-2025 15:02:19 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"STFDY19208"}
[30-Jul-2025 15:02:19 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 15:02:19 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: STFDY19208
[30-Jul-2025 15:02:19 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/testing/consultarEstado.php con datos: {"hookalias":"STFDY19208"}
[30-Jul-2025 15:02:19 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 15:02:19 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[]}
[30-Jul-2025 15:02:24 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 15:02:24 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 15:02:24 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 15:02:24 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 15:02:24 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 15:02:24 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 15:02:24 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"STFDY19208"}'
[30-Jul-2025 15:02:24 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"STFDY19208"}
[30-Jul-2025 15:02:24 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 15:02:24 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: STFDY19208
[30-Jul-2025 15:02:24 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/testing/consultarEstado.php con datos: {"hookalias":"STFDY19208"}
[30-Jul-2025 15:02:24 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 15:02:24 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[]}
[30-Jul-2025 15:02:29 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 15:02:29 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 15:02:29 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 15:02:29 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 15:02:29 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 15:02:29 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 15:02:29 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"STFDY19208"}'
[30-Jul-2025 15:02:29 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"STFDY19208"}
[30-Jul-2025 15:02:29 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 15:02:29 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: STFDY19208
[30-Jul-2025 15:02:29 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/testing/consultarEstado.php con datos: {"hookalias":"STFDY19208"}
[30-Jul-2025 15:02:29 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 15:02:29 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[]}
[30-Jul-2025 15:02:34 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 15:02:34 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 15:02:34 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 15:02:34 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 15:02:34 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 15:02:34 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 15:02:34 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"STFDY19208"}'
[30-Jul-2025 15:02:34 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"STFDY19208"}
[30-Jul-2025 15:02:34 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 15:02:34 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: STFDY19208
[30-Jul-2025 15:02:34 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/testing/consultarEstado.php con datos: {"hookalias":"STFDY19208"}
[30-Jul-2025 15:02:34 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 15:02:34 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[]}
[30-Jul-2025 15:02:39 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 15:02:39 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 15:02:39 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 15:02:39 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 15:02:39 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 15:02:39 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 15:02:39 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"STFDY19208"}'
[30-Jul-2025 15:02:39 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"STFDY19208"}
[30-Jul-2025 15:02:39 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 15:02:39 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: STFDY19208
[30-Jul-2025 15:02:39 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/testing/consultarEstado.php con datos: {"hookalias":"STFDY19208"}
[30-Jul-2025 15:02:39 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 15:02:39 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[]}
[30-Jul-2025 15:02:44 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 15:02:44 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 15:02:44 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 15:02:44 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 15:02:44 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 15:02:44 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 15:02:44 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"STFDY19208"}'
[30-Jul-2025 15:02:44 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"STFDY19208"}
[30-Jul-2025 15:02:44 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 15:02:44 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: STFDY19208
[30-Jul-2025 15:02:44 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/testing/consultarEstado.php con datos: {"hookalias":"STFDY19208"}
[30-Jul-2025 15:02:44 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 15:02:44 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[]}
[30-Jul-2025 15:02:49 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 15:02:49 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 15:02:49 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 15:02:49 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 15:02:49 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 15:02:49 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 15:02:49 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"STFDY19208"}'
[30-Jul-2025 15:02:49 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"STFDY19208"}
[30-Jul-2025 15:02:49 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 15:02:49 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: STFDY19208
[30-Jul-2025 15:02:49 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/testing/consultarEstado.php con datos: {"hookalias":"STFDY19208"}
[30-Jul-2025 15:02:49 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 15:02:49 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[]}
[30-Jul-2025 15:02:54 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 15:02:54 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 15:02:54 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 15:02:54 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 15:02:54 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 15:02:54 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 15:02:54 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"STFDY19208"}'
[30-Jul-2025 15:02:54 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"STFDY19208"}
[30-Jul-2025 15:02:54 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 15:02:54 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: STFDY19208
[30-Jul-2025 15:02:54 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/testing/consultarEstado.php con datos: {"hookalias":"STFDY19208"}
[30-Jul-2025 15:02:54 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 15:02:54 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[]}
[30-Jul-2025 15:02:59 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 15:02:59 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 15:02:59 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 15:02:59 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 15:02:59 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 15:02:59 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 15:02:59 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"STFDY19208"}'
[30-Jul-2025 15:02:59 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"STFDY19208"}
[30-Jul-2025 15:02:59 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 15:02:59 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: STFDY19208
[30-Jul-2025 15:02:59 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/testing/consultarEstado.php con datos: {"hookalias":"STFDY19208"}
[30-Jul-2025 15:02:59 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 15:02:59 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[]}
[30-Jul-2025 15:03:04 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 15:03:04 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 15:03:04 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 15:03:04 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 15:03:04 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 15:03:04 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 15:03:04 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"STFDY19208"}'
[30-Jul-2025 15:03:04 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"STFDY19208"}
[30-Jul-2025 15:03:04 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 15:03:04 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: STFDY19208
[30-Jul-2025 15:03:04 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/testing/consultarEstado.php con datos: {"hookalias":"STFDY19208"}
[30-Jul-2025 15:03:04 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 15:03:04 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[]}
[30-Jul-2025 15:03:09 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 15:03:09 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 15:03:09 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 15:03:09 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 15:03:09 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 15:03:09 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 15:03:09 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"STFDY19208"}'
[30-Jul-2025 15:03:09 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"STFDY19208"}
[30-Jul-2025 15:03:09 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 15:03:09 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: STFDY19208
[30-Jul-2025 15:03:09 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/testing/consultarEstado.php con datos: {"hookalias":"STFDY19208"}
[30-Jul-2025 15:03:09 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 15:03:09 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[]}
[30-Jul-2025 15:03:14 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 15:03:14 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 15:03:14 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 15:03:14 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 15:03:14 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 15:03:14 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 15:03:14 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"STFDY19208"}'
[30-Jul-2025 15:03:14 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"STFDY19208"}
[30-Jul-2025 15:03:14 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 15:03:14 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: STFDY19208
[30-Jul-2025 15:03:14 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/testing/consultarEstado.php con datos: {"hookalias":"STFDY19208"}
[30-Jul-2025 15:03:14 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 15:03:14 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[]}
[30-Jul-2025 15:12:15 Europe/Berlin] registrarAlias.php - Método HTTP: PUT
[30-Jul-2025 15:12:15 Europe/Berlin] registrarAlias.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 15:12:15 Europe/Berlin] registrarAlias.php - Query String: 
[30-Jul-2025 15:12:15 Europe/Berlin] registrarAlias.php - GET params: []
[30-Jul-2025 15:12:15 Europe/Berlin] registrarAlias.php - POST params: []
[30-Jul-2025 15:12:15 Europe/Berlin] registrarAlias.php - Input raw length: 120
[30-Jul-2025 15:12:15 Europe/Berlin] registrarAlias.php - Input raw: '{"hookalias":"SLOVT46592","amount":133020,"description":"Compra en Tienda de Nike","created_at":"30\/07\/2025 10:12:13"}'
[30-Jul-2025 15:12:15 Europe/Berlin] registrarAlias.php - JSON decode result: {"hookalias":"SLOVT46592","amount":133020,"description":"Compra en Tienda de Nike","created_at":"30\/07\/2025 10:12:13"}
[30-Jul-2025 15:12:15 Europe/Berlin] registrarAlias.php - JSON last error: No error
[30-Jul-2025 15:12:15 Europe/Berlin] registrarAlias.php - hookalias encontrado en JSON body: SLOVT46592
[30-Jul-2025 15:12:15 Europe/Berlin] registrarAlias.php - amount encontrado en JSON body: 133020
[30-Jul-2025 15:12:15 Europe/Berlin] registrarAlias.php - description encontrado en JSON body: Compra en Tienda de Nike
[30-Jul-2025 15:12:15 Europe/Berlin] registrarAlias.php - created_at encontrado en JSON body: 30/07/2025 10:12:13
[30-Jul-2025 15:12:15 Europe/Berlin] registrarAlias.php - Enviando a: https://zavidoro.com.py/bancard/registrarAlias.php con datos: {"hookalias":"SLOVT46592","amount":133020,"description":"Compra en Tienda de Nike","created_at":"30\/07\/2025 10:12:13"}
[30-Jul-2025 15:12:15 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 15:12:15 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 15:12:15 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 15:12:15 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 15:12:15 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 15:12:15 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 15:12:15 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SLOVT46592"}'
[30-Jul-2025 15:12:15 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SLOVT46592"}
[30-Jul-2025 15:12:15 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 15:12:15 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SLOVT46592
[30-Jul-2025 15:12:15 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/testing/consultarEstado.php con datos: {"hookalias":"SLOVT46592"}
[30-Jul-2025 15:12:15 Europe/Berlin] registrarAlias.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 15:12:15 Europe/Berlin] registrarAlias.php - Respuesta del servidor remoto - Body: {"status":"success","message":"Alias registrado correctamente"}
[30-Jul-2025 15:12:15 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 15:12:15 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[{"id":88,"hook_alias":"SLOVT46592","status":"pendiente","url":null,"description":"Compra en Tienda de Nike","fecha_creacion":"Jul 30 2025 10:12AM","fecha_actualizacion":null,"response_code":null,"response_description":null,"amount":null,"currency":null,"installment_number":null,"product_description":null,"date_time":null,"ticket_number":null,"authorization_code":null,"commerce_name":null,"branch_name":null,"bin":null,"merchant_code":null,"payer_name":null,"payer_lastname":null,"card_last_numbers":null,"account_type":null,"created_at":null}]}
[30-Jul-2025 15:12:20 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 15:12:20 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 15:12:20 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 15:12:20 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 15:12:20 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 15:12:20 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 15:12:20 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SLOVT46592"}'
[30-Jul-2025 15:12:20 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SLOVT46592"}
[30-Jul-2025 15:12:20 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 15:12:20 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SLOVT46592
[30-Jul-2025 15:12:20 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/testing/consultarEstado.php con datos: {"hookalias":"SLOVT46592"}
[30-Jul-2025 15:12:20 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 15:12:20 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[{"id":88,"hook_alias":"SLOVT46592","status":"pendiente","url":null,"description":"Compra en Tienda de Nike","fecha_creacion":"Jul 30 2025 10:12AM","fecha_actualizacion":null,"response_code":null,"response_description":null,"amount":null,"currency":null,"installment_number":null,"product_description":null,"date_time":null,"ticket_number":null,"authorization_code":null,"commerce_name":null,"branch_name":null,"bin":null,"merchant_code":null,"payer_name":null,"payer_lastname":null,"card_last_numbers":null,"account_type":null,"created_at":null}]}
[30-Jul-2025 15:12:25 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 15:12:25 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 15:12:25 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 15:12:25 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 15:12:25 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 15:12:25 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 15:12:25 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SLOVT46592"}'
[30-Jul-2025 15:12:25 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SLOVT46592"}
[30-Jul-2025 15:12:25 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 15:12:25 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SLOVT46592
[30-Jul-2025 15:12:25 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/testing/consultarEstado.php con datos: {"hookalias":"SLOVT46592"}
[30-Jul-2025 15:12:25 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 15:12:25 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[{"id":88,"hook_alias":"SLOVT46592","status":"pendiente","url":null,"description":"Compra en Tienda de Nike","fecha_creacion":"Jul 30 2025 10:12AM","fecha_actualizacion":null,"response_code":null,"response_description":null,"amount":null,"currency":null,"installment_number":null,"product_description":null,"date_time":null,"ticket_number":null,"authorization_code":null,"commerce_name":null,"branch_name":null,"bin":null,"merchant_code":null,"payer_name":null,"payer_lastname":null,"card_last_numbers":null,"account_type":null,"created_at":null}]}
[30-Jul-2025 15:12:30 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 15:12:30 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 15:12:30 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 15:12:30 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 15:12:30 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 15:12:30 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 15:12:30 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SLOVT46592"}'
[30-Jul-2025 15:12:30 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SLOVT46592"}
[30-Jul-2025 15:12:30 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 15:12:30 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SLOVT46592
[30-Jul-2025 15:12:30 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/testing/consultarEstado.php con datos: {"hookalias":"SLOVT46592"}
[30-Jul-2025 15:12:30 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 15:12:30 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[{"id":88,"hook_alias":"SLOVT46592","status":"pendiente","url":null,"description":"Compra en Tienda de Nike","fecha_creacion":"Jul 30 2025 10:12AM","fecha_actualizacion":null,"response_code":null,"response_description":null,"amount":null,"currency":null,"installment_number":null,"product_description":null,"date_time":null,"ticket_number":null,"authorization_code":null,"commerce_name":null,"branch_name":null,"bin":null,"merchant_code":null,"payer_name":null,"payer_lastname":null,"card_last_numbers":null,"account_type":null,"created_at":null}]}
[30-Jul-2025 15:12:35 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 15:12:35 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 15:12:35 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 15:12:35 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 15:12:35 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 15:12:35 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 15:12:35 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SLOVT46592"}'
[30-Jul-2025 15:12:35 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SLOVT46592"}
[30-Jul-2025 15:12:35 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 15:12:35 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SLOVT46592
[30-Jul-2025 15:12:35 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/testing/consultarEstado.php con datos: {"hookalias":"SLOVT46592"}
[30-Jul-2025 15:12:35 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 15:12:35 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[{"id":88,"hook_alias":"SLOVT46592","status":"pendiente","url":null,"description":"Compra en Tienda de Nike","fecha_creacion":"Jul 30 2025 10:12AM","fecha_actualizacion":null,"response_code":null,"response_description":null,"amount":null,"currency":null,"installment_number":null,"product_description":null,"date_time":null,"ticket_number":null,"authorization_code":null,"commerce_name":null,"branch_name":null,"bin":null,"merchant_code":null,"payer_name":null,"payer_lastname":null,"card_last_numbers":null,"account_type":null,"created_at":null}]}
[30-Jul-2025 15:12:40 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 15:12:40 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 15:12:40 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 15:12:40 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 15:12:40 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 15:12:40 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 15:12:40 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SLOVT46592"}'
[30-Jul-2025 15:12:40 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SLOVT46592"}
[30-Jul-2025 15:12:40 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 15:12:40 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SLOVT46592
[30-Jul-2025 15:12:40 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/testing/consultarEstado.php con datos: {"hookalias":"SLOVT46592"}
[30-Jul-2025 15:12:40 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 15:12:40 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[{"id":88,"hook_alias":"SLOVT46592","status":"pendiente","url":null,"description":"Compra en Tienda de Nike","fecha_creacion":"Jul 30 2025 10:12AM","fecha_actualizacion":null,"response_code":null,"response_description":null,"amount":null,"currency":null,"installment_number":null,"product_description":null,"date_time":null,"ticket_number":null,"authorization_code":null,"commerce_name":null,"branch_name":null,"bin":null,"merchant_code":null,"payer_name":null,"payer_lastname":null,"card_last_numbers":null,"account_type":null,"created_at":null}]}
[30-Jul-2025 15:12:45 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 15:12:45 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 15:12:45 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 15:12:45 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 15:12:45 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 15:12:45 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 15:12:45 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SLOVT46592"}'
[30-Jul-2025 15:12:45 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SLOVT46592"}
[30-Jul-2025 15:12:45 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 15:12:45 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SLOVT46592
[30-Jul-2025 15:12:45 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/testing/consultarEstado.php con datos: {"hookalias":"SLOVT46592"}
[30-Jul-2025 15:12:45 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 15:12:45 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[{"id":88,"hook_alias":"SLOVT46592","status":"confirmed","url":null,"description":"Compra en Tienda de Nike","fecha_creacion":"Jul 30 2025 10:12AM","fecha_actualizacion":"Jul 30 2025 10:12AM","response_code":"00","response_description":"Pago Exitoso","amount":"133020.00","currency":"GS","installment_number":1,"product_description":null,"date_time":{"date":"2025-07-30 10:12:42.873000","timezone_type":3,"timezone":"UTC"},"ticket_number":"**********","authorization_code":"874072","commerce_name":"ZAVIDORO CORPORATIONS SUC.PY.","branch_name":"NIKE-SOL","bin":"************","merchant_code":"553149","payer_name":null,"payer_lastname":null,"card_last_numbers":2045,"account_type":"TC","created_at":null}]}
[30-Jul-2025 15:17:50 Europe/Berlin] registrarAlias.php - Método HTTP: PUT
[30-Jul-2025 15:17:50 Europe/Berlin] registrarAlias.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 15:17:50 Europe/Berlin] registrarAlias.php - Query String: 
[30-Jul-2025 15:17:50 Europe/Berlin] registrarAlias.php - GET params: []
[30-Jul-2025 15:17:50 Europe/Berlin] registrarAlias.php - POST params: []
[30-Jul-2025 15:17:50 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 15:17:50 Europe/Berlin] registrarAlias.php - Input raw length: 120
[30-Jul-2025 15:17:50 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 15:17:50 Europe/Berlin] registrarAlias.php - Input raw: '{"hookalias":"SPROU95610","amount":177360,"description":"Compra en Tienda de Nike","created_at":"30\/07\/2025 10:17:49"}'
[30-Jul-2025 15:17:50 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 15:17:50 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 15:17:50 Europe/Berlin] registrarAlias.php - JSON decode result: {"hookalias":"SPROU95610","amount":177360,"description":"Compra en Tienda de Nike","created_at":"30\/07\/2025 10:17:49"}
[30-Jul-2025 15:17:50 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 15:17:50 Europe/Berlin] registrarAlias.php - JSON last error: No error
[30-Jul-2025 15:17:50 Europe/Berlin] registrarAlias.php - hookalias encontrado en JSON body: SPROU95610
[30-Jul-2025 15:17:50 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 15:17:50 Europe/Berlin] registrarAlias.php - amount encontrado en JSON body: 177360
[30-Jul-2025 15:17:50 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SPROU95610"}'
[30-Jul-2025 15:17:50 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SPROU95610"}
[30-Jul-2025 15:17:50 Europe/Berlin] registrarAlias.php - description encontrado en JSON body: Compra en Tienda de Nike
[30-Jul-2025 15:17:50 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 15:17:50 Europe/Berlin] registrarAlias.php - created_at encontrado en JSON body: 30/07/2025 10:17:49
[30-Jul-2025 15:17:50 Europe/Berlin] registrarAlias.php - Enviando a: https://zavidoro.com.py/bancard/registrarAlias.php con datos: {"hookalias":"SPROU95610","amount":177360,"description":"Compra en Tienda de Nike","created_at":"30\/07\/2025 10:17:49"}
[30-Jul-2025 15:17:50 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SPROU95610
[30-Jul-2025 15:17:50 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/testing/consultarEstado.php con datos: {"hookalias":"SPROU95610"}
[30-Jul-2025 15:17:51 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 15:17:51 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[]}
[30-Jul-2025 15:17:51 Europe/Berlin] registrarAlias.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 15:17:51 Europe/Berlin] registrarAlias.php - Respuesta del servidor remoto - Body: {"status":"success","message":"Alias registrado correctamente"}
[30-Jul-2025 15:17:55 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 15:17:55 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 15:17:55 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 15:17:55 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 15:17:55 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 15:17:55 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 15:17:55 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SPROU95610"}'
[30-Jul-2025 15:17:55 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SPROU95610"}
[30-Jul-2025 15:17:55 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 15:17:55 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SPROU95610
[30-Jul-2025 15:17:55 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/testing/consultarEstado.php con datos: {"hookalias":"SPROU95610"}
[30-Jul-2025 15:17:56 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 15:17:56 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[{"id":89,"hook_alias":"SPROU95610","status":"pendiente","url":null,"description":"Compra en Tienda de Nike","fecha_creacion":"Jul 30 2025 10:17AM","fecha_actualizacion":null,"response_code":null,"response_description":null,"amount":null,"currency":null,"installment_number":null,"product_description":null,"date_time":null,"ticket_number":null,"authorization_code":null,"commerce_name":null,"branch_name":null,"bin":null,"merchant_code":null,"payer_name":null,"payer_lastname":null,"card_last_numbers":null,"account_type":null,"created_at":null}]}
[30-Jul-2025 15:18:00 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 15:18:00 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 15:18:00 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 15:18:00 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 15:18:00 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 15:18:00 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 15:18:00 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SPROU95610"}'
[30-Jul-2025 15:18:00 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SPROU95610"}
[30-Jul-2025 15:18:00 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 15:18:00 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SPROU95610
[30-Jul-2025 15:18:00 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/testing/consultarEstado.php con datos: {"hookalias":"SPROU95610"}
[30-Jul-2025 15:18:01 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 15:18:01 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[{"id":89,"hook_alias":"SPROU95610","status":"pendiente","url":null,"description":"Compra en Tienda de Nike","fecha_creacion":"Jul 30 2025 10:17AM","fecha_actualizacion":null,"response_code":null,"response_description":null,"amount":null,"currency":null,"installment_number":null,"product_description":null,"date_time":null,"ticket_number":null,"authorization_code":null,"commerce_name":null,"branch_name":null,"bin":null,"merchant_code":null,"payer_name":null,"payer_lastname":null,"card_last_numbers":null,"account_type":null,"created_at":null}]}
[30-Jul-2025 15:18:05 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 15:18:05 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 15:18:05 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 15:18:05 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 15:18:05 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 15:18:05 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 15:18:05 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SPROU95610"}'
[30-Jul-2025 15:18:05 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SPROU95610"}
[30-Jul-2025 15:18:05 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 15:18:05 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SPROU95610
[30-Jul-2025 15:18:05 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/testing/consultarEstado.php con datos: {"hookalias":"SPROU95610"}
[30-Jul-2025 15:18:06 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 15:18:06 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[{"id":89,"hook_alias":"SPROU95610","status":"pendiente","url":null,"description":"Compra en Tienda de Nike","fecha_creacion":"Jul 30 2025 10:17AM","fecha_actualizacion":null,"response_code":null,"response_description":null,"amount":null,"currency":null,"installment_number":null,"product_description":null,"date_time":null,"ticket_number":null,"authorization_code":null,"commerce_name":null,"branch_name":null,"bin":null,"merchant_code":null,"payer_name":null,"payer_lastname":null,"card_last_numbers":null,"account_type":null,"created_at":null}]}
[30-Jul-2025 15:18:10 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 15:18:10 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 15:18:10 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 15:18:10 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 15:18:10 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 15:18:10 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 15:18:10 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SPROU95610"}'
[30-Jul-2025 15:18:10 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SPROU95610"}
[30-Jul-2025 15:18:10 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 15:18:10 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SPROU95610
[30-Jul-2025 15:18:10 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/testing/consultarEstado.php con datos: {"hookalias":"SPROU95610"}
[30-Jul-2025 15:18:11 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 15:18:11 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[{"id":89,"hook_alias":"SPROU95610","status":"pendiente","url":null,"description":"Compra en Tienda de Nike","fecha_creacion":"Jul 30 2025 10:17AM","fecha_actualizacion":null,"response_code":null,"response_description":null,"amount":null,"currency":null,"installment_number":null,"product_description":null,"date_time":null,"ticket_number":null,"authorization_code":null,"commerce_name":null,"branch_name":null,"bin":null,"merchant_code":null,"payer_name":null,"payer_lastname":null,"card_last_numbers":null,"account_type":null,"created_at":null}]}
[30-Jul-2025 15:18:15 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 15:18:15 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 15:18:15 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 15:18:15 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 15:18:15 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 15:18:15 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 15:18:15 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SPROU95610"}'
[30-Jul-2025 15:18:15 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SPROU95610"}
[30-Jul-2025 15:18:15 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 15:18:15 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SPROU95610
[30-Jul-2025 15:18:15 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/testing/consultarEstado.php con datos: {"hookalias":"SPROU95610"}
[30-Jul-2025 15:18:16 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 15:18:16 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[{"id":89,"hook_alias":"SPROU95610","status":"pendiente","url":null,"description":"Compra en Tienda de Nike","fecha_creacion":"Jul 30 2025 10:17AM","fecha_actualizacion":null,"response_code":null,"response_description":null,"amount":null,"currency":null,"installment_number":null,"product_description":null,"date_time":null,"ticket_number":null,"authorization_code":null,"commerce_name":null,"branch_name":null,"bin":null,"merchant_code":null,"payer_name":null,"payer_lastname":null,"card_last_numbers":null,"account_type":null,"created_at":null}]}
[30-Jul-2025 15:18:20 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 15:18:20 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 15:18:20 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 15:18:20 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 15:18:20 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 15:18:20 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 15:18:20 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SPROU95610"}'
[30-Jul-2025 15:18:20 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SPROU95610"}
[30-Jul-2025 15:18:20 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 15:18:20 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SPROU95610
[30-Jul-2025 15:18:20 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/testing/consultarEstado.php con datos: {"hookalias":"SPROU95610"}
[30-Jul-2025 15:18:21 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 15:18:21 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[{"id":89,"hook_alias":"SPROU95610","status":"pendiente","url":null,"description":"Compra en Tienda de Nike","fecha_creacion":"Jul 30 2025 10:17AM","fecha_actualizacion":null,"response_code":null,"response_description":null,"amount":null,"currency":null,"installment_number":null,"product_description":null,"date_time":null,"ticket_number":null,"authorization_code":null,"commerce_name":null,"branch_name":null,"bin":null,"merchant_code":null,"payer_name":null,"payer_lastname":null,"card_last_numbers":null,"account_type":null,"created_at":null}]}
[30-Jul-2025 15:18:25 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 15:18:25 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 15:18:25 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 15:18:25 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 15:18:25 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 15:18:25 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 15:18:25 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SPROU95610"}'
[30-Jul-2025 15:18:25 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SPROU95610"}
[30-Jul-2025 15:18:25 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 15:18:25 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SPROU95610
[30-Jul-2025 15:18:25 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/testing/consultarEstado.php con datos: {"hookalias":"SPROU95610"}
[30-Jul-2025 15:18:26 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 15:18:26 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[{"id":89,"hook_alias":"SPROU95610","status":"pendiente","url":null,"description":"Compra en Tienda de Nike","fecha_creacion":"Jul 30 2025 10:17AM","fecha_actualizacion":null,"response_code":null,"response_description":null,"amount":null,"currency":null,"installment_number":null,"product_description":null,"date_time":null,"ticket_number":null,"authorization_code":null,"commerce_name":null,"branch_name":null,"bin":null,"merchant_code":null,"payer_name":null,"payer_lastname":null,"card_last_numbers":null,"account_type":null,"created_at":null}]}
[30-Jul-2025 15:18:30 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 15:18:30 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 15:18:30 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 15:18:30 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 15:18:30 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 15:18:30 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 15:18:30 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SPROU95610"}'
[30-Jul-2025 15:18:30 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SPROU95610"}
[30-Jul-2025 15:18:30 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 15:18:30 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SPROU95610
[30-Jul-2025 15:18:30 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/testing/consultarEstado.php con datos: {"hookalias":"SPROU95610"}
[30-Jul-2025 15:18:31 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 15:18:31 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[{"id":89,"hook_alias":"SPROU95610","status":"confirmed","url":null,"description":"Compra en Tienda de Nike","fecha_creacion":"Jul 30 2025 10:18AM","fecha_actualizacion":"Jul 30 2025 10:18AM","response_code":"00","response_description":"Pago Exitoso","amount":"177360.00","currency":"GS","installment_number":1,"product_description":null,"date_time":{"date":"2025-07-30 10:18:30.403000","timezone_type":3,"timezone":"UTC"},"ticket_number":"**********","authorization_code":"874074","commerce_name":"ZAVIDORO CORPORATIONS SUC.PY.","branch_name":"NIKE-SOL","bin":"************","merchant_code":"553149","payer_name":null,"payer_lastname":null,"card_last_numbers":2045,"account_type":"TC","created_at":null}]}
[30-Jul-2025 15:51:53 Europe/Berlin] [TEST CONNECTION INIT] Parámetros recibidos - TestPC: ************** | CodeEmp: 1 | CodSuc: 33
[30-Jul-2025 15:51:53 Europe/Berlin] [TEST CONNECTION IP] Conectando a IP directa | Servidor: ************** | Base de datos: SUC33 | Usuario: SA | Empresa: zavidoro
[30-Jul-2025 15:51:53 Europe/Berlin] [TEST CONNECTION] Tipo: Prueba IP (TESTPC:**************, CODEMP:1, CODSUC:33) | Servidor: ************** | Base de datos: SUC33 | Usuario: SA | Timestamp: 2025-07-30 15:51:53
[30-Jul-2025 15:51:53 Europe/Berlin] [TEST CONNECTION SUCCESS] Prueba IP (TESTPC:**************, CODEMP:1, CODSUC:33) | Servidor: ************** | Base de datos: SUC33 | Conexión establecida exitosamente
[30-Jul-2025 15:52:07 Europe/Berlin] registrarAlias.php - Método HTTP: PUT
[30-Jul-2025 15:52:07 Europe/Berlin] registrarAlias.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 15:52:07 Europe/Berlin] registrarAlias.php - Query String: 
[30-Jul-2025 15:52:07 Europe/Berlin] registrarAlias.php - GET params: []
[30-Jul-2025 15:52:07 Europe/Berlin] registrarAlias.php - POST params: []
[30-Jul-2025 15:52:07 Europe/Berlin] registrarAlias.php - Input raw length: 119
[30-Jul-2025 15:52:07 Europe/Berlin] registrarAlias.php - Input raw: '{"hookalias":"SCFNZ10396","amount":88680,"description":"Compra en Tienda de Nike","created_at":"30\/07\/2025 10:52:06"}'
[30-Jul-2025 15:52:07 Europe/Berlin] registrarAlias.php - JSON decode result: {"hookalias":"SCFNZ10396","amount":88680,"description":"Compra en Tienda de Nike","created_at":"30\/07\/2025 10:52:06"}
[30-Jul-2025 15:52:07 Europe/Berlin] registrarAlias.php - JSON last error: No error
[30-Jul-2025 15:52:07 Europe/Berlin] registrarAlias.php - hookalias encontrado en JSON body: SCFNZ10396
[30-Jul-2025 15:52:07 Europe/Berlin] registrarAlias.php - amount encontrado en JSON body: 88680
[30-Jul-2025 15:52:07 Europe/Berlin] registrarAlias.php - description encontrado en JSON body: Compra en Tienda de Nike
[30-Jul-2025 15:52:07 Europe/Berlin] registrarAlias.php - created_at encontrado en JSON body: 30/07/2025 10:52:06
[30-Jul-2025 15:52:07 Europe/Berlin] registrarAlias.php - Enviando a: https://zavidoro.com.py/bancard/registrarAlias.php con datos: {"hookalias":"SCFNZ10396","amount":88680,"description":"Compra en Tienda de Nike","created_at":"30\/07\/2025 10:52:06"}
[30-Jul-2025 15:52:07 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 15:52:07 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 15:52:07 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 15:52:07 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 15:52:07 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 15:52:07 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 15:52:07 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SCFNZ10396"}'
[30-Jul-2025 15:52:07 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SCFNZ10396"}
[30-Jul-2025 15:52:07 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 15:52:07 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SCFNZ10396
[30-Jul-2025 15:52:07 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/testing/consultarEstado.php con datos: {"hookalias":"SCFNZ10396"}
[30-Jul-2025 15:52:08 Europe/Berlin] registrarAlias.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 15:52:08 Europe/Berlin] registrarAlias.php - Respuesta del servidor remoto - Body: {"status":"success","message":"Alias registrado correctamente"}
[30-Jul-2025 15:52:08 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 15:52:08 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[{"id":90,"hook_alias":"SCFNZ10396","status":"pendiente","url":null,"description":"Compra en Tienda de Nike","fecha_creacion":"Jul 30 2025 10:52AM","fecha_actualizacion":null,"response_code":null,"response_description":null,"amount":null,"currency":null,"installment_number":null,"product_description":null,"date_time":null,"ticket_number":null,"authorization_code":null,"commerce_name":null,"branch_name":null,"bin":null,"merchant_code":null,"payer_name":null,"payer_lastname":null,"card_last_numbers":null,"account_type":null,"created_at":null}]}
[30-Jul-2025 15:52:12 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 15:52:12 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 15:52:12 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 15:52:12 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 15:52:12 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 15:52:12 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 15:52:12 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SCFNZ10396"}'
[30-Jul-2025 15:52:12 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SCFNZ10396"}
[30-Jul-2025 15:52:12 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 15:52:12 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SCFNZ10396
[30-Jul-2025 15:52:12 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/testing/consultarEstado.php con datos: {"hookalias":"SCFNZ10396"}
[30-Jul-2025 15:52:13 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 15:52:13 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[{"id":90,"hook_alias":"SCFNZ10396","status":"pendiente","url":null,"description":"Compra en Tienda de Nike","fecha_creacion":"Jul 30 2025 10:52AM","fecha_actualizacion":null,"response_code":null,"response_description":null,"amount":null,"currency":null,"installment_number":null,"product_description":null,"date_time":null,"ticket_number":null,"authorization_code":null,"commerce_name":null,"branch_name":null,"bin":null,"merchant_code":null,"payer_name":null,"payer_lastname":null,"card_last_numbers":null,"account_type":null,"created_at":null}]}
[30-Jul-2025 15:59:30 Europe/Berlin] [TEST CONNECTION INIT] Parámetros recibidos - TestPC: ************** | CodeEmp: 1 | CodSuc: 33
[30-Jul-2025 15:59:30 Europe/Berlin] [TEST CONNECTION IP] Conectando a IP directa | Servidor: ************** | Base de datos: SUC33 | Usuario: SA | Empresa: zavidoro
[30-Jul-2025 15:59:30 Europe/Berlin] [TEST CONNECTION] Tipo: Prueba IP (TESTPC:**************, CODEMP:1, CODSUC:33) | Servidor: ************** | Base de datos: SUC33 | Usuario: SA | Timestamp: 2025-07-30 15:59:30
[30-Jul-2025 15:59:30 Europe/Berlin] [TEST CONNECTION SUCCESS] Prueba IP (TESTPC:**************, CODEMP:1, CODSUC:33) | Servidor: ************** | Base de datos: SUC33 | Conexión establecida exitosamente
[30-Jul-2025 15:59:37 Europe/Berlin] registrarAlias.php - Método HTTP: PUT
[30-Jul-2025 15:59:37 Europe/Berlin] registrarAlias.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 15:59:37 Europe/Berlin] registrarAlias.php - Query String: 
[30-Jul-2025 15:59:37 Europe/Berlin] registrarAlias.php - GET params: []
[30-Jul-2025 15:59:37 Europe/Berlin] registrarAlias.php - POST params: []
[30-Jul-2025 15:59:37 Europe/Berlin] registrarAlias.php - Input raw length: 119
[30-Jul-2025 15:59:37 Europe/Berlin] registrarAlias.php - Input raw: '{"hookalias":"SVGRU35780","amount":22170,"description":"Compra en Tienda de Nike","created_at":"30\/07\/2025 10:59:36"}'
[30-Jul-2025 15:59:37 Europe/Berlin] registrarAlias.php - JSON decode result: {"hookalias":"SVGRU35780","amount":22170,"description":"Compra en Tienda de Nike","created_at":"30\/07\/2025 10:59:36"}
[30-Jul-2025 15:59:37 Europe/Berlin] registrarAlias.php - JSON last error: No error
[30-Jul-2025 15:59:37 Europe/Berlin] registrarAlias.php - hookalias encontrado en JSON body: SVGRU35780
[30-Jul-2025 15:59:37 Europe/Berlin] registrarAlias.php - amount encontrado en JSON body: 22170
[30-Jul-2025 15:59:37 Europe/Berlin] registrarAlias.php - description encontrado en JSON body: Compra en Tienda de Nike
[30-Jul-2025 15:59:37 Europe/Berlin] registrarAlias.php - created_at encontrado en JSON body: 30/07/2025 10:59:36
[30-Jul-2025 15:59:37 Europe/Berlin] registrarAlias.php - Enviando a: https://zavidoro.com.py/bancard/registrarAlias.php con datos: {"hookalias":"SVGRU35780","amount":22170,"description":"Compra en Tienda de Nike","created_at":"30\/07\/2025 10:59:36"}
[30-Jul-2025 15:59:37 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 15:59:37 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 15:59:37 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 15:59:37 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 15:59:37 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 15:59:37 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 15:59:37 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SVGRU35780"}'
[30-Jul-2025 15:59:37 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SVGRU35780"}
[30-Jul-2025 15:59:37 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 15:59:37 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SVGRU35780
[30-Jul-2025 15:59:37 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/testing/consultarEstado.php con datos: {"hookalias":"SVGRU35780"}
[30-Jul-2025 15:59:38 Europe/Berlin] registrarAlias.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 15:59:38 Europe/Berlin] registrarAlias.php - Respuesta del servidor remoto - Body: {"status":"success","message":"Alias registrado correctamente"}
[30-Jul-2025 15:59:38 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 15:59:38 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[{"id":91,"hook_alias":"SVGRU35780","status":"pendiente","url":null,"description":"Compra en Tienda de Nike","fecha_creacion":"Jul 30 2025 10:59AM","fecha_actualizacion":null,"response_code":null,"response_description":null,"amount":null,"currency":null,"installment_number":null,"product_description":null,"date_time":null,"ticket_number":null,"authorization_code":null,"commerce_name":null,"branch_name":null,"bin":null,"merchant_code":null,"payer_name":null,"payer_lastname":null,"card_last_numbers":null,"account_type":null,"created_at":null}]}
[30-Jul-2025 15:59:42 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 15:59:42 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 15:59:42 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 15:59:42 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 15:59:42 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 15:59:42 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 15:59:42 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SVGRU35780"}'
[30-Jul-2025 15:59:42 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SVGRU35780"}
[30-Jul-2025 15:59:42 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 15:59:42 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SVGRU35780
[30-Jul-2025 15:59:42 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/testing/consultarEstado.php con datos: {"hookalias":"SVGRU35780"}
[30-Jul-2025 15:59:43 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 15:59:43 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[{"id":91,"hook_alias":"SVGRU35780","status":"pendiente","url":null,"description":"Compra en Tienda de Nike","fecha_creacion":"Jul 30 2025 10:59AM","fecha_actualizacion":null,"response_code":null,"response_description":null,"amount":null,"currency":null,"installment_number":null,"product_description":null,"date_time":null,"ticket_number":null,"authorization_code":null,"commerce_name":null,"branch_name":null,"bin":null,"merchant_code":null,"payer_name":null,"payer_lastname":null,"card_last_numbers":null,"account_type":null,"created_at":null}]}
[30-Jul-2025 19:20:22 Europe/Berlin] [TEST CONNECTION INIT] Parámetros recibidos - TestPC: ************** | CodeEmp: 1 | CodSuc: 33
[30-Jul-2025 19:20:22 Europe/Berlin] [TEST CONNECTION INIT] Parámetros recibidos - TestPC: ************** | CodeEmp: 1 | CodSuc: 33
[30-Jul-2025 19:20:22 Europe/Berlin] [TEST CONNECTION IP] Conectando a IP directa | Servidor: ************** | Base de datos: SUC33 | Usuario: SA | Empresa: zavidoro
[30-Jul-2025 19:20:22 Europe/Berlin] [TEST CONNECTION] Tipo: Prueba IP (TESTPC:**************, CODEMP:1, CODSUC:33) | Servidor: ************** | Base de datos: SUC33 | Usuario: SA | Timestamp: 2025-07-30 19:20:22
[30-Jul-2025 19:20:22 Europe/Berlin] [TEST CONNECTION IP] Conectando a IP directa | Servidor: ************** | Base de datos: SUC33 | Usuario: SA | Empresa: zavidoro
[30-Jul-2025 19:20:22 Europe/Berlin] [TEST CONNECTION] Tipo: Prueba IP (TESTPC:**************, CODEMP:1, CODSUC:33) | Servidor: ************** | Base de datos: SUC33 | Usuario: SA | Timestamp: 2025-07-30 19:20:22
[30-Jul-2025 19:20:22 Europe/Berlin] [TEST CONNECTION SUCCESS] Prueba IP (TESTPC:**************, CODEMP:1, CODSUC:33) | Servidor: ************** | Base de datos: SUC33 | Conexión establecida exitosamente
[30-Jul-2025 19:20:22 Europe/Berlin] [TEST CONNECTION SUCCESS] Prueba IP (TESTPC:**************, CODEMP:1, CODSUC:33) | Servidor: ************** | Base de datos: SUC33 | Conexión establecida exitosamente
[30-Jul-2025 19:31:35 Europe/Berlin] [TEST CONNECTION INIT] Parámetros recibidos - TestPC: ************** | CodeEmp: 1 | CodSuc: 33
[30-Jul-2025 19:31:35 Europe/Berlin] [TEST CONNECTION IP] Conectando a IP directa | Servidor: ************** | Base de datos: SUC33 | Usuario: SA | Empresa: zavidoro
[30-Jul-2025 19:31:35 Europe/Berlin] [TEST CONNECTION] Tipo: Prueba IP (TESTPC:**************, CODEMP:1, CODSUC:33) | Servidor: ************** | Base de datos: SUC33 | Usuario: SA | Timestamp: 2025-07-30 19:31:35
[30-Jul-2025 19:31:35 Europe/Berlin] [TEST CONNECTION SUCCESS] Prueba IP (TESTPC:**************, CODEMP:1, CODSUC:33) | Servidor: ************** | Base de datos: SUC33 | Conexión establecida exitosamente
[30-Jul-2025 19:40:09 Europe/Berlin] [TEST CONNECTION INIT] Parámetros recibidos - TestPC: ************** | CodeEmp: 1 | CodSuc: 33
[30-Jul-2025 19:40:09 Europe/Berlin] [TEST CONNECTION IP] Conectando a IP directa | Servidor: ************** | Base de datos: SUC33 | Usuario: SA | Empresa: zavidoro
[30-Jul-2025 19:40:09 Europe/Berlin] [TEST CONNECTION] Tipo: Prueba IP (TESTPC:**************, CODEMP:1, CODSUC:33) | Servidor: ************** | Base de datos: SUC33 | Usuario: SA | Timestamp: 2025-07-30 19:40:09
[30-Jul-2025 19:40:09 Europe/Berlin] [TEST CONNECTION SUCCESS] Prueba IP (TESTPC:**************, CODEMP:1, CODSUC:33) | Servidor: ************** | Base de datos: SUC33 | Conexión establecida exitosamente
[30-Jul-2025 19:41:02 Europe/Berlin] registrarAlias.php - Método HTTP: PUT
[30-Jul-2025 19:41:02 Europe/Berlin] registrarAlias.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 19:41:02 Europe/Berlin] registrarAlias.php - Query String: 
[30-Jul-2025 19:41:02 Europe/Berlin] registrarAlias.php - GET params: []
[30-Jul-2025 19:41:02 Europe/Berlin] registrarAlias.php - POST params: []
[30-Jul-2025 19:41:02 Europe/Berlin] registrarAlias.php - Input raw length: 119
[30-Jul-2025 19:41:02 Europe/Berlin] registrarAlias.php - Input raw: '{"hookalias":"SSNKG28154","amount":22170,"description":"Compra en Tienda de Nike","created_at":"30\/07\/2025 14:41:01"}'
[30-Jul-2025 19:41:02 Europe/Berlin] registrarAlias.php - JSON decode result: {"hookalias":"SSNKG28154","amount":22170,"description":"Compra en Tienda de Nike","created_at":"30\/07\/2025 14:41:01"}
[30-Jul-2025 19:41:02 Europe/Berlin] registrarAlias.php - JSON last error: No error
[30-Jul-2025 19:41:02 Europe/Berlin] registrarAlias.php - hookalias encontrado en JSON body: SSNKG28154
[30-Jul-2025 19:41:02 Europe/Berlin] registrarAlias.php - amount encontrado en JSON body: 22170
[30-Jul-2025 19:41:02 Europe/Berlin] registrarAlias.php - description encontrado en JSON body: Compra en Tienda de Nike
[30-Jul-2025 19:41:02 Europe/Berlin] registrarAlias.php - created_at encontrado en JSON body: 30/07/2025 14:41:01
[30-Jul-2025 19:41:02 Europe/Berlin] registrarAlias.php - Enviando a: https://zavidoro.com.py/bancard/registrarAlias.php con datos: {"hookalias":"SSNKG28154","amount":22170,"description":"Compra en Tienda de Nike","created_at":"30\/07\/2025 14:41:01"}
[30-Jul-2025 19:41:02 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 19:41:02 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 19:41:02 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 19:41:02 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 19:41:02 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 19:41:02 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 19:41:02 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SSNKG28154"}'
[30-Jul-2025 19:41:02 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SSNKG28154"}
[30-Jul-2025 19:41:02 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 19:41:02 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SSNKG28154
[30-Jul-2025 19:41:02 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/testing/consultarEstado.php con datos: {"hookalias":"SSNKG28154"}
[30-Jul-2025 19:41:03 Europe/Berlin] registrarAlias.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 19:41:03 Europe/Berlin] registrarAlias.php - Respuesta del servidor remoto - Body: {"status":"success","message":"Alias registrado correctamente"}
[30-Jul-2025 19:41:03 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 19:41:03 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[{"id":92,"hook_alias":"SSNKG28154","status":"pendiente","url":null,"description":"Compra en Tienda de Nike","fecha_creacion":"Jul 30 2025  2:41PM","fecha_actualizacion":null,"response_code":null,"response_description":null,"amount":null,"currency":null,"installment_number":null,"product_description":null,"date_time":null,"ticket_number":null,"authorization_code":null,"commerce_name":null,"branch_name":null,"bin":null,"merchant_code":null,"payer_name":null,"payer_lastname":null,"card_last_numbers":null,"account_type":null,"created_at":null}]}
[30-Jul-2025 19:41:07 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 19:41:07 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 19:41:07 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 19:41:07 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 19:41:07 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 19:41:07 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 19:41:07 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SSNKG28154"}'
[30-Jul-2025 19:41:07 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SSNKG28154"}
[30-Jul-2025 19:41:07 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 19:41:07 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SSNKG28154
[30-Jul-2025 19:41:07 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/testing/consultarEstado.php con datos: {"hookalias":"SSNKG28154"}
[30-Jul-2025 19:41:08 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 19:41:08 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[{"id":92,"hook_alias":"SSNKG28154","status":"pendiente","url":null,"description":"Compra en Tienda de Nike","fecha_creacion":"Jul 30 2025  2:41PM","fecha_actualizacion":null,"response_code":null,"response_description":null,"amount":null,"currency":null,"installment_number":null,"product_description":null,"date_time":null,"ticket_number":null,"authorization_code":null,"commerce_name":null,"branch_name":null,"bin":null,"merchant_code":null,"payer_name":null,"payer_lastname":null,"card_last_numbers":null,"account_type":null,"created_at":null}]}
[30-Jul-2025 19:41:13 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 19:41:13 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 19:41:13 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 19:41:13 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 19:41:13 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 19:41:13 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 19:41:13 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SSNKG28154"}'
[30-Jul-2025 19:41:13 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SSNKG28154"}
[30-Jul-2025 19:41:13 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 19:41:13 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SSNKG28154
[30-Jul-2025 19:41:13 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/testing/consultarEstado.php con datos: {"hookalias":"SSNKG28154"}
[30-Jul-2025 19:41:13 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 19:41:13 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[{"id":92,"hook_alias":"SSNKG28154","status":"pendiente","url":null,"description":"Compra en Tienda de Nike","fecha_creacion":"Jul 30 2025  2:41PM","fecha_actualizacion":null,"response_code":null,"response_description":null,"amount":null,"currency":null,"installment_number":null,"product_description":null,"date_time":null,"ticket_number":null,"authorization_code":null,"commerce_name":null,"branch_name":null,"bin":null,"merchant_code":null,"payer_name":null,"payer_lastname":null,"card_last_numbers":null,"account_type":null,"created_at":null}]}
[30-Jul-2025 19:41:17 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 19:41:17 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 19:41:17 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 19:41:17 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 19:41:17 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 19:41:17 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 19:41:17 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SSNKG28154"}'
[30-Jul-2025 19:41:17 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SSNKG28154"}
[30-Jul-2025 19:41:18 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 19:41:18 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SSNKG28154
[30-Jul-2025 19:41:18 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/testing/consultarEstado.php con datos: {"hookalias":"SSNKG28154"}
[30-Jul-2025 19:41:18 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 19:41:18 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[{"id":92,"hook_alias":"SSNKG28154","status":"confirmed","url":null,"description":"Compra en Tienda de Nike","fecha_creacion":"Jul 30 2025  2:41PM","fecha_actualizacion":"Jul 30 2025  2:41PM","response_code":"00","response_description":"Pago Exitoso","amount":"22170.00","currency":"GS","installment_number":1,"product_description":null,"date_time":{"date":"2025-07-30 14:41:16.867000","timezone_type":3,"timezone":"UTC"},"ticket_number":"**********","authorization_code":"874128","commerce_name":"ZAVIDORO CORPORATIONS SUC.PY.","branch_name":"NIKE-SOL","bin":"************","merchant_code":"553149","payer_name":null,"payer_lastname":null,"card_last_numbers":2045,"account_type":"TC","created_at":null}]}
[30-Jul-2025 20:02:45 Europe/Berlin] [TEST CONNECTION INIT] Parámetros recibidos - TestPC: ************** | CodeEmp: 1 | CodSuc: 33
[30-Jul-2025 20:02:45 Europe/Berlin] [TEST CONNECTION IP] Conectando a IP directa | Servidor: ************** | Base de datos: SUC33 | Usuario: SA | Empresa: zavidoro
[30-Jul-2025 20:02:45 Europe/Berlin] [TEST CONNECTION] Tipo: Prueba IP (TESTPC:**************, CODEMP:1, CODSUC:33) | Servidor: ************** | Base de datos: SUC33 | Usuario: SA | Timestamp: 2025-07-30 20:02:45
[30-Jul-2025 20:02:45 Europe/Berlin] [TEST CONNECTION SUCCESS] Prueba IP (TESTPC:**************, CODEMP:1, CODSUC:33) | Servidor: ************** | Base de datos: SUC33 | Conexión establecida exitosamente
[30-Jul-2025 20:03:09 Europe/Berlin] [TEST CONNECTION INIT] Parámetros recibidos - TestPC: ************** | CodeEmp: 1 | CodSuc: 33
[30-Jul-2025 20:03:09 Europe/Berlin] [TEST CONNECTION IP] Conectando a IP directa | Servidor: ************** | Base de datos: SUC33 | Usuario: SA | Empresa: zavidoro
[30-Jul-2025 20:03:09 Europe/Berlin] [TEST CONNECTION] Tipo: Prueba IP (TESTPC:**************, CODEMP:1, CODSUC:33) | Servidor: ************** | Base de datos: SUC33 | Usuario: SA | Timestamp: 2025-07-30 20:03:09
[30-Jul-2025 20:03:09 Europe/Berlin] [TEST CONNECTION SUCCESS] Prueba IP (TESTPC:**************, CODEMP:1, CODSUC:33) | Servidor: ************** | Base de datos: SUC33 | Conexión establecida exitosamente
[30-Jul-2025 20:04:32 Europe/Berlin] registrarAlias.php - Método HTTP: PUT
[30-Jul-2025 20:04:32 Europe/Berlin] registrarAlias.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 20:04:32 Europe/Berlin] registrarAlias.php - Query String: 
[30-Jul-2025 20:04:32 Europe/Berlin] registrarAlias.php - GET params: []
[30-Jul-2025 20:04:32 Europe/Berlin] registrarAlias.php - POST params: []
[30-Jul-2025 20:04:32 Europe/Berlin] registrarAlias.php - Input raw length: 119
[30-Jul-2025 20:04:32 Europe/Berlin] registrarAlias.php - Input raw: '{"hookalias":"SQWRN47196","amount":44340,"description":"Compra en Tienda de Nike","created_at":"30\/07\/2025 15:04:31"}'
[30-Jul-2025 20:04:32 Europe/Berlin] registrarAlias.php - JSON decode result: {"hookalias":"SQWRN47196","amount":44340,"description":"Compra en Tienda de Nike","created_at":"30\/07\/2025 15:04:31"}
[30-Jul-2025 20:04:32 Europe/Berlin] registrarAlias.php - JSON last error: No error
[30-Jul-2025 20:04:32 Europe/Berlin] registrarAlias.php - hookalias encontrado en JSON body: SQWRN47196
[30-Jul-2025 20:04:32 Europe/Berlin] registrarAlias.php - amount encontrado en JSON body: 44340
[30-Jul-2025 20:04:32 Europe/Berlin] registrarAlias.php - description encontrado en JSON body: Compra en Tienda de Nike
[30-Jul-2025 20:04:32 Europe/Berlin] registrarAlias.php - created_at encontrado en JSON body: 30/07/2025 15:04:31
[30-Jul-2025 20:04:32 Europe/Berlin] registrarAlias.php - Enviando a: https://zavidoro.com.py/bancard/registrarAlias.php con datos: {"hookalias":"SQWRN47196","amount":44340,"description":"Compra en Tienda de Nike","created_at":"30\/07\/2025 15:04:31"}
[30-Jul-2025 20:04:32 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 20:04:32 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 20:04:32 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 20:04:32 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 20:04:32 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 20:04:32 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 20:04:32 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SQWRN47196"}'
[30-Jul-2025 20:04:32 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SQWRN47196"}
[30-Jul-2025 20:04:32 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 20:04:32 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SQWRN47196
[30-Jul-2025 20:04:32 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/testing/consultarEstado.php con datos: {"hookalias":"SQWRN47196"}
[30-Jul-2025 20:04:33 Europe/Berlin] registrarAlias.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 20:04:33 Europe/Berlin] registrarAlias.php - Respuesta del servidor remoto - Body: {"status":"success","message":"Alias registrado correctamente"}
[30-Jul-2025 20:04:33 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 20:04:33 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[{"id":93,"hook_alias":"SQWRN47196","status":"pendiente","url":null,"description":"Compra en Tienda de Nike","fecha_creacion":"Jul 30 2025  3:04PM","fecha_actualizacion":null,"response_code":null,"response_description":null,"amount":null,"currency":null,"installment_number":null,"product_description":null,"date_time":null,"ticket_number":null,"authorization_code":null,"commerce_name":null,"branch_name":null,"bin":null,"merchant_code":null,"payer_name":null,"payer_lastname":null,"card_last_numbers":null,"account_type":null,"created_at":null}]}
[30-Jul-2025 20:04:37 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 20:04:37 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 20:04:37 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 20:04:37 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 20:04:37 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 20:04:37 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 20:04:37 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SQWRN47196"}'
[30-Jul-2025 20:04:37 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SQWRN47196"}
[30-Jul-2025 20:04:37 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 20:04:37 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SQWRN47196
[30-Jul-2025 20:04:37 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/testing/consultarEstado.php con datos: {"hookalias":"SQWRN47196"}
[30-Jul-2025 20:04:38 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 20:04:38 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[{"id":93,"hook_alias":"SQWRN47196","status":"pendiente","url":null,"description":"Compra en Tienda de Nike","fecha_creacion":"Jul 30 2025  3:04PM","fecha_actualizacion":null,"response_code":null,"response_description":null,"amount":null,"currency":null,"installment_number":null,"product_description":null,"date_time":null,"ticket_number":null,"authorization_code":null,"commerce_name":null,"branch_name":null,"bin":null,"merchant_code":null,"payer_name":null,"payer_lastname":null,"card_last_numbers":null,"account_type":null,"created_at":null}]}
[30-Jul-2025 20:04:42 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 20:04:42 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 20:04:42 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 20:04:42 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 20:04:42 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 20:04:42 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 20:04:42 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SQWRN47196"}'
[30-Jul-2025 20:04:42 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SQWRN47196"}
[30-Jul-2025 20:04:42 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 20:04:42 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SQWRN47196
[30-Jul-2025 20:04:42 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/testing/consultarEstado.php con datos: {"hookalias":"SQWRN47196"}
[30-Jul-2025 20:04:43 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 20:04:43 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[{"id":93,"hook_alias":"SQWRN47196","status":"pendiente","url":null,"description":"Compra en Tienda de Nike","fecha_creacion":"Jul 30 2025  3:04PM","fecha_actualizacion":null,"response_code":null,"response_description":null,"amount":null,"currency":null,"installment_number":null,"product_description":null,"date_time":null,"ticket_number":null,"authorization_code":null,"commerce_name":null,"branch_name":null,"bin":null,"merchant_code":null,"payer_name":null,"payer_lastname":null,"card_last_numbers":null,"account_type":null,"created_at":null}]}
[30-Jul-2025 20:04:47 Europe/Berlin] consultarEstado.php - Método HTTP: POST
[30-Jul-2025 20:04:47 Europe/Berlin] consultarEstado.php - Content-Type: application/json; charset=utf-8
[30-Jul-2025 20:04:47 Europe/Berlin] consultarEstado.php - Query String: 
[30-Jul-2025 20:04:47 Europe/Berlin] consultarEstado.php - GET params: []
[30-Jul-2025 20:04:47 Europe/Berlin] consultarEstado.php - POST params: []
[30-Jul-2025 20:04:47 Europe/Berlin] consultarEstado.php - Input raw length: 26
[30-Jul-2025 20:04:47 Europe/Berlin] consultarEstado.php - Input raw: '{"hookalias":"SQWRN47196"}'
[30-Jul-2025 20:04:47 Europe/Berlin] consultarEstado.php - JSON decode result: {"hookalias":"SQWRN47196"}
[30-Jul-2025 20:04:47 Europe/Berlin] consultarEstado.php - JSON last error: No error
[30-Jul-2025 20:04:47 Europe/Berlin] consultarEstado.php - hookalias encontrado en JSON body: SQWRN47196
[30-Jul-2025 20:04:47 Europe/Berlin] consultarEstado.php - Enviando a: https://zavidoro.com.py/testing/consultarEstado.php con datos: {"hookalias":"SQWRN47196"}
[30-Jul-2025 20:04:48 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - HTTP Code: 200
[30-Jul-2025 20:04:48 Europe/Berlin] consultarEstado.php - Respuesta del servidor remoto - Body: {"status":"success","data":[{"id":93,"hook_alias":"SQWRN47196","status":"confirmed","url":null,"description":"Compra en Tienda de Nike","fecha_creacion":"Jul 30 2025  3:04PM","fecha_actualizacion":"Jul 30 2025  3:04PM","response_code":"00","response_description":"Pago Exitoso","amount":"44340.00","currency":"GS","installment_number":1,"product_description":null,"date_time":{"date":"2025-07-30 15:04:46.217000","timezone_type":3,"timezone":"UTC"},"ticket_number":"**********","authorization_code":"874138","commerce_name":"ZAVIDORO CORPORATIONS SUC.PY.","branch_name":"NIKE-SOL","bin":"************","merchant_code":"553149","payer_name":null,"payer_lastname":null,"card_last_numbers":2045,"account_type":"TC","created_at":null}]}
