[2025-07-10 03:14:47] Iniciando webservice - Data: {"input_data":null}
[2025-07-10 03:14:47] Ejecutando en base de datos principal - Data: {"has_procedure":false,"has_sql":false}
[2025-07-10 03:18:03] Iniciando webservice - Data: {"input_data":null}
[2025-07-10 03:18:03] Ejecutando en base de datos principal - Data: {"has_procedure":false,"has_sql":false}
[2025-07-10 03:19:29] Iniciando webservice - Data: {"input_data":{"codemp":"1","codsuc":"33","proc":"SP_INSERT_AUDIT_TRANSAC","TEST":"true","TESTPC":"MSALAS-M","params":{"@IDCOMPROBANTE":"0","@CODCMP":"FB","@TALONARIO":"1","@PREFIJO":"001-001","@NUMERO":"33226","@FECHA":"2025-07-09 10:05:33","@OPERADOR":"000","@TOTAL":"23130.00","@TIMESTAMP":"2025-07-09 10:05:33","@HOSTNAME":"EBARBA-Z","@ACCION":"X","@LOGAPP":"{\"codigoEstado\":400,\"codigoEstadoBAS\":1012,\"cuerpo\":\"El n\u00famero de cup\u00f3n manual ya fu\u00e9 utilizado.(ControlNroCupon)(SP_ICR_COMPROB_VENTA_PAGO_TARJETA)\",\"informacionExtra\":null,\"pdf\":null,\"pdFerror\":null,\"informacionProximaConsulta\":null}","@HOOKALIAS":"SANIZ16940"}}}
[2025-07-10 03:19:29] Ejecutando en base de datos de sucursal - Data: {"codemp":"1","codsuc":"33","has_procedure":true,"has_sql":false,"test":"true","testPC":"MSALAS-M"}
[2025-07-10 03:19:29] Ejecutando procedimiento en sucursal - Data: {"procedure":"SP_INSERT_AUDIT_TRANSAC","params":{"@IDCOMPROBANTE":"0","@CODCMP":"FB","@TALONARIO":"1","@PREFIJO":"001-001","@NUMERO":"33226","@FECHA":"2025-07-09 10:05:33","@OPERADOR":"000","@TOTAL":"23130.00","@TIMESTAMP":"2025-07-09 10:05:33","@HOSTNAME":"EBARBA-Z","@ACCION":"X","@LOGAPP":"{\"codigoEstado\":400,\"codigoEstadoBAS\":1012,\"cuerpo\":\"El n\u00famero de cup\u00f3n manual ya fu\u00e9 utilizado.(ControlNroCupon)(SP_ICR_COMPROB_VENTA_PAGO_TARJETA)\",\"informacionExtra\":null,\"pdf\":null,\"pdFerror\":null,\"informacionProximaConsulta\":null}","@HOOKALIAS":"SANIZ16940"},"codemp":"1","codsuc":"33","test":"true","testPC":"MSALAS-M"}
[2025-07-10 03:19:29] Usando conexión de prueba para procedimiento - Data: {"test":"true","testPC":"MSALAS-M","procedure":"SP_INSERT_AUDIT_TRANSAC"}
[2025-07-10 03:19:29] Procedimiento ejecutado exitosamente en sucursal - Data: {"procedure":"SP_INSERT_AUDIT_TRANSAC","results_count":0,"codemp":"1","codsuc":"33"}
[2025-07-10 04:31:21] Iniciando webservice - Data: {"input_data":{"codemp":"1","codsuc":"33","proc":"SP_INSERT_AUDIT_TRANSAC","TEST":"true","TESTPC":"MSALAS-M","params":{"@IDCOMPROBANTE":"0","@CODCMP":"FB","@TALONARIO":"1","@PREFIJO":"001-001","@NUMERO":"33226","@FECHA":"2025-07-09 10:05:33","@OPERADOR":"000","@TOTAL":"23130.00","@TIMESTAMP":"2025-07-09 10:05:33","@HOSTNAME":"EBARBA-Z","@ACCION":"X","@LOGAPP":"{\"codigoEstado\":400,\"codigoEstadoBAS\":1012,\"cuerpo\":\"El n\u00famero de cup\u00f3n manual ya fu\u00e9 utilizado.(ControlNroCupon)(SP_ICR_COMPROB_VENTA_PAGO_TARJETA)\",\"informacionExtra\":null,\"pdf\":null,\"pdFerror\":null,\"informacionProximaConsulta\":null}","@HOOKALIAS":"SANIZ16940"}}}
[2025-07-10 04:31:21] Ejecutando en base de datos de sucursal - Data: {"codemp":"1","codsuc":"33","has_procedure":true,"has_sql":false,"test":"true","testPC":"MSALAS-M"}
[2025-07-10 04:31:21] Ejecutando procedimiento en sucursal - Data: {"procedure":"SP_INSERT_AUDIT_TRANSAC","params":{"@IDCOMPROBANTE":"0","@CODCMP":"FB","@TALONARIO":"1","@PREFIJO":"001-001","@NUMERO":"33226","@FECHA":"2025-07-09 10:05:33","@OPERADOR":"000","@TOTAL":"23130.00","@TIMESTAMP":"2025-07-09 10:05:33","@HOSTNAME":"EBARBA-Z","@ACCION":"X","@LOGAPP":"{\"codigoEstado\":400,\"codigoEstadoBAS\":1012,\"cuerpo\":\"El n\u00famero de cup\u00f3n manual ya fu\u00e9 utilizado.(ControlNroCupon)(SP_ICR_COMPROB_VENTA_PAGO_TARJETA)\",\"informacionExtra\":null,\"pdf\":null,\"pdFerror\":null,\"informacionProximaConsulta\":null}","@HOOKALIAS":"SANIZ16940"},"codemp":"1","codsuc":"33","test":"true","testPC":"MSALAS-M"}
[2025-07-10 04:31:21] Usando conexión de prueba para procedimiento - Data: {"test":"true","testPC":"MSALAS-M","procedure":"SP_INSERT_AUDIT_TRANSAC"}
[2025-07-10 04:31:21] Procedimiento ejecutado exitosamente en sucursal - Data: {"procedure":"SP_INSERT_AUDIT_TRANSAC","results_count":0,"codemp":"1","codsuc":"33"}
[2025-07-10 04:41:09] Iniciando webservice - Data: {"input_data":{"codemp":"1","codsuc":"33","proc":"SP_INSERT_AUDIT_TRANSAC","TEST":"true","TESTPC":"MSALAS-M","sql":"SELECT * FROM PVSucursales"}}
[2025-07-10 04:41:09] Ejecutando en base de datos de sucursal - Data: {"codemp":"1","codsuc":"33","has_procedure":true,"has_sql":true,"test":"true","testPC":"MSALAS-M"}
[2025-07-10 04:41:09] Ejecutando procedimiento en sucursal - Data: {"procedure":"SP_INSERT_AUDIT_TRANSAC","params":[],"codemp":"1","codsuc":"33","test":"true","testPC":"MSALAS-M"}
[2025-07-10 04:41:09] Usando conexión de prueba para procedimiento - Data: {"test":"true","testPC":"MSALAS-M","procedure":"SP_INSERT_AUDIT_TRANSAC"}
[2025-07-10 04:41:09] Error crítico en webservice - Data: {"error":"Fallo la consulta: SQLSTATE: 23000, Code: 515, Message: [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Cannot insert the value NULL into column 'PREFIJO', table 'SUC33.dbo.AUDIT_TRANSAC'; column does not allow nulls. INSERT fails.; SQLSTATE: 01000, Code: 3621, Message: [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]The statement has been terminated.","trace":"#0 C:\\xampp\\htdocs\\bancard\\webservice.php(116): DatabaseQueryExecutor->executeProcedure('SP_INSERT_AUDIT...', Array, '1', '33', 'true', 'MSALAS-M')\n#1 C:\\xampp\\htdocs\\bancard\\webservice.php(273): ejecutarWebservice(Array)\n#2 {main}"}
[2025-07-10 04:41:25] Iniciando webservice - Data: {"input_data":{"codemp":"1","codsuc":"33","TEST":"true","TESTPC":"MSALAS-M","sql":"SELECT * FROM PVSucursales"}}
[2025-07-10 04:41:25] Ejecutando en base de datos de sucursal - Data: {"codemp":"1","codsuc":"33","has_procedure":false,"has_sql":true,"test":"true","testPC":"MSALAS-M"}
[2025-07-10 04:41:25] Ejecutando SQL directo en sucursal - Data: {"sql":"SELECT * FROM PVSucursales","params":[],"codemp":"1","codsuc":"33","test":"true","testPC":"MSALAS-M"}
[2025-07-10 04:41:25] Usando conexión de prueba para SQL - Data: {"test":"true","testPC":"MSALAS-M","sql":"SELECT * FROM PVSucursales"}
[2025-07-10 04:41:25] SQL ejecutado exitosamente en sucursal - Data: {"sql":"SELECT * FROM PVSucursales","results_count":12,"codemp":"1","codsuc":"33"}
[2025-07-10 04:42:46] Iniciando webservice - Data: {"input_data":{"TEST":"true","TESTPC":"MSALAS-M","codemp":"1","codsuc":"33","sql":"SELECT * FROM PVSucursales"}}
[2025-07-10 04:42:46] Ejecutando en base de datos de sucursal - Data: {"codemp":"1","codsuc":"33","has_procedure":false,"has_sql":true,"test":"true","testPC":"MSALAS-M"}
[2025-07-10 04:42:46] Ejecutando SQL directo en sucursal - Data: {"sql":"SELECT * FROM PVSucursales","params":[],"codemp":"1","codsuc":"33","test":"true","testPC":"MSALAS-M"}
[2025-07-10 04:42:46] Usando conexión de prueba para SQL - Data: {"test":"true","testPC":"MSALAS-M","sql":"SELECT * FROM PVSucursales"}
[2025-07-10 04:42:47] SQL ejecutado exitosamente en sucursal - Data: {"sql":"SELECT * FROM PVSucursales","results_count":12,"codemp":"1","codsuc":"33"}
[2025-07-10 04:42:52] Iniciando webservice - Data: {"input_data":{"codemp":"1","codsuc":"33","sql":"SELECT * FROM PVSucursales"}}
[2025-07-10 04:42:52] Ejecutando en base de datos de sucursal - Data: {"codemp":"1","codsuc":"33","has_procedure":false,"has_sql":true,"test":null,"testPC":null}
[2025-07-10 04:42:52] Ejecutando SQL directo en sucursal - Data: {"sql":"SELECT * FROM PVSucursales","params":[],"codemp":"1","codsuc":"33","test":null,"testPC":null}
[2025-07-10 04:42:52] SQL ejecutado exitosamente en sucursal - Data: {"sql":"SELECT * FROM PVSucursales","results_count":12,"codemp":"1","codsuc":"33"}
[2025-07-10 04:44:41] Iniciando webservice - Data: {"input_data":{"TEST":"true","TESTPC":"MSALAS-M","codemp":"1","codsuc":"33","sql":"SELECT * FROM PVSucursales"}}
[2025-07-10 04:44:41] Ejecutando en base de datos de sucursal - Data: {"codemp":"1","codsuc":"33","has_procedure":false,"has_sql":true,"test":"true","testPC":"MSALAS-M"}
[2025-07-10 04:44:41] Ejecutando SQL directo en sucursal - Data: {"sql":"SELECT * FROM PVSucursales","params":[],"codemp":"1","codsuc":"33","test":"true","testPC":"MSALAS-M"}
[2025-07-10 04:44:41] Usando conexión de prueba para SQL - Data: {"test":"true","testPC":"MSALAS-M","sql":"SELECT * FROM PVSucursales"}
[2025-07-10 04:44:41] SQL ejecutado exitosamente en sucursal - Data: {"sql":"SELECT * FROM PVSucursales","results_count":12,"codemp":"1","codsuc":"33"}
[2025-07-11 12:25:05] Iniciando webservice - Data: {"input_data":{"codemp":"1","codsuc":"33","sql":"SELECT * FROM PVSucursales"}}
[2025-07-11 12:25:05] Ejecutando en base de datos de sucursal - Data: {"codemp":"1","codsuc":"33","has_procedure":false,"has_sql":true,"test":null,"testPC":null}
[2025-07-11 12:25:05] Ejecutando SQL directo en sucursal - Data: {"sql":"SELECT * FROM PVSucursales","params":[],"codemp":"1","codsuc":"33","test":null,"testPC":null}
[2025-07-11 12:25:54] Error crítico en webservice - Data: {"error":"Fall\u00f3 la conexi\u00f3n a la Base de Datos (Sucursal (CODEMP:1, CODSUC:33)): SQLSTATE: 08001, Code: 64, Message: [Microsoft][ODBC Driver 17 for SQL Server]Named Pipes Provider: Could not open a connection to SQL Server [64]. ; SQLSTATE: HYT00, Code: 0, Message: [Microsoft][ODBC Driver 17 for SQL Server]Login timeout expired; SQLSTATE: 08001, Code: 64, Message: [Microsoft][ODBC Driver 17 for SQL Server]A network-related or instance-specific error has occurred while establishing a connection to SQL Server. Server is not found or not accessible. Check if instance name is correct and if SQL Server is configured to allow remote connections. For more information see SQL Server Books Online.","trace":"#0 C:\\xampp\\htdocs\\bancard\\components\\DatabaseConnectionManager.php(71): DatabaseConnectionManager->establishConnection('srv-suc33.swoos...', Array, 'Sucursal (CODEM...')\n#1 C:\\xampp\\htdocs\\bancard\\components\\DatabaseConnectionManager.php(152): DatabaseConnectionManager->createSucursalConnection(1, 33)\n#2 C:\\xampp\\htdocs\\bancard\\components\\DatabaseQueryExecutor.php(25): DatabaseConnectionManager->createConnection('1', '33', NULL, NULL, NULL)\n#3 C:\\xampp\\htdocs\\bancard\\webservice.php(178): DatabaseQueryExecutor->executeQuery('SELECT * FROM P...', Array, '1', '33')\n#4 C:\\xampp\\htdocs\\bancard\\webservice.php(273): ejecutarWebservice(Array)\n#5 {main}"}
[2025-07-11 14:40:56] Iniciando webservice - Data: {"input_data":{"codemp":"1","codsuc":"33","sql":"SELECT * FROM PVSucursales"}}
[2025-07-11 14:40:56] Ejecutando en base de datos de sucursal - Data: {"codemp":"1","codsuc":"33","has_procedure":false,"has_sql":true,"test":null,"testPC":null}
[2025-07-11 14:40:56] Ejecutando SQL directo en sucursal - Data: {"sql":"SELECT * FROM PVSucursales","params":[],"codemp":"1","codsuc":"33","test":null,"testPC":null}
[2025-07-11 14:40:56] SQL ejecutado exitosamente en sucursal - Data: {"sql":"SELECT * FROM PVSucursales","results_count":12,"codemp":"1","codsuc":"33"}
[2025-07-11 14:40:59] Iniciando webservice - Data: {"input_data":{"codemp":"1","codsuc":"03","sql":"SELECT * FROM PVSucursales"}}
[2025-07-11 14:40:59] Ejecutando en base de datos de sucursal - Data: {"codemp":"1","codsuc":"03","has_procedure":false,"has_sql":true,"test":null,"testPC":null}
[2025-07-11 14:40:59] Ejecutando SQL directo en sucursal - Data: {"sql":"SELECT * FROM PVSucursales","params":[],"codemp":"1","codsuc":"03","test":null,"testPC":null}
[2025-07-11 14:40:59] SQL ejecutado exitosamente en sucursal - Data: {"sql":"SELECT * FROM PVSucursales","results_count":12,"codemp":"1","codsuc":"03"}
[2025-07-30 15:18:32] Iniciando webservice - Data: {"input_data":{"codemp":"1","codsuc":"33","TEST":"true","TESTPC":"**************","proc":"SP_INSERT_AUDIT_TRANSAC","params":{"@CODCMP":"FB","@PREFIJO":"001-001","@FECHA":"2025-07-30 10:18:31","@OPERADOR":"000","@TOTAL":"177360.00","@TIMESTAMP":"2025-07-30 10:18:31","@HOSTNAME":"MOVIL1-SUC33","@ACCION":"X","@LOGAPP":"{\"codigoEstado\":400,\"codigoEstadoBAS\":1012,\"cuerpo\":\"Could not find prepared statement with handle 3.\\r\\nNo se encontr\u00a2 un talonario para el prefijo '001-001' y el c\u00a2digo de POS 'MOVIL1-SUC33    '.\",\"informacionExtra\":null,\"pdf\":null,\"pdFerror\":null,\"informacionProximaConsulta\":null}","@HOOKALIAS":"SPROU95610"}}}
[2025-07-30 15:18:32] Ejecutando en base de datos de sucursal - Data: {"codemp":"1","codsuc":"33","has_procedure":true,"has_sql":false,"test":"true","testPC":"**************"}
[2025-07-30 15:18:32] Ejecutando procedimiento en sucursal - Data: {"procedure":"SP_INSERT_AUDIT_TRANSAC","params":{"@CODCMP":"FB","@PREFIJO":"001-001","@FECHA":"2025-07-30 10:18:31","@OPERADOR":"000","@TOTAL":"177360.00","@TIMESTAMP":"2025-07-30 10:18:31","@HOSTNAME":"MOVIL1-SUC33","@ACCION":"X","@LOGAPP":"{\"codigoEstado\":400,\"codigoEstadoBAS\":1012,\"cuerpo\":\"Could not find prepared statement with handle 3.\\r\\nNo se encontr\u00a2 un talonario para el prefijo '001-001' y el c\u00a2digo de POS 'MOVIL1-SUC33    '.\",\"informacionExtra\":null,\"pdf\":null,\"pdFerror\":null,\"informacionProximaConsulta\":null}","@HOOKALIAS":"SPROU95610"},"codemp":"1","codsuc":"33","test":"true","testPC":"**************"}
[2025-07-30 15:18:32] Usando conexión de prueba para procedimiento - Data: {"test":"true","testPC":"**************","procedure":"SP_INSERT_AUDIT_TRANSAC"}
[2025-07-30 15:18:32] Error crítico en webservice - Data: {"error":"Fallo la consulta: SQLSTATE: 42000, Code: 8114, Message: [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Error converting data type varchar to int.","trace":"#0 C:\\xampp\\htdocs\\appmovil\\api_testing\\webservice.php(116): DatabaseQueryExecutor->executeProcedure('SP_INSERT_AUDIT...', Array, '1', '33', 'true', '**************')\n#1 C:\\xampp\\htdocs\\appmovil\\api_testing\\webservice.php(273): ejecutarWebservice(Array)\n#2 {main}"}
[2025-07-30 15:18:32] Iniciando webservice - Data: {"input_data":{"codemp":"1","codsuc":"33","proc":"SP_INSERT_AUDIT_TRANSAC_ITEMS","TEST":"true","TESTPC":"**************","params":{"@CODITM":"201840,4","@CANTIDAD1":"4","@PRECIO":"22170.0","@IMPORTE":"88680.00"}}}
[2025-07-30 15:18:32] Ejecutando en base de datos de sucursal - Data: {"codemp":"1","codsuc":"33","has_procedure":true,"has_sql":false,"test":"true","testPC":"**************"}
[2025-07-30 15:18:32] Ejecutando procedimiento en sucursal - Data: {"procedure":"SP_INSERT_AUDIT_TRANSAC_ITEMS","params":{"@CODITM":"201840,4","@CANTIDAD1":"4","@PRECIO":"22170.0","@IMPORTE":"88680.00"},"codemp":"1","codsuc":"33","test":"true","testPC":"**************"}
[2025-07-30 15:18:32] Usando conexión de prueba para procedimiento - Data: {"test":"true","testPC":"**************","procedure":"SP_INSERT_AUDIT_TRANSAC_ITEMS"}
[2025-07-30 15:18:32] Error crítico en webservice - Data: {"error":"Fallo la consulta: SQLSTATE: 42000, Code: 8114, Message: [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Error converting data type varchar to int.","trace":"#0 C:\\xampp\\htdocs\\appmovil\\api_testing\\webservice.php(116): DatabaseQueryExecutor->executeProcedure('SP_INSERT_AUDIT...', Array, '1', '33', 'true', '**************')\n#1 C:\\xampp\\htdocs\\appmovil\\api_testing\\webservice.php(273): ejecutarWebservice(Array)\n#2 {main}"}
[2025-07-30 15:18:32] Iniciando webservice - Data: {"input_data":{"codemp":"1","codsuc":"33","proc":"SP_INSERT_AUDIT_TRANSAC_ITEMS","TEST":"true","TESTPC":"**************","params":{"@CODITM":"201840,3","@CANTIDAD1":"4","@PRECIO":"22170.0","@IMPORTE":"88680.00"}}}
[2025-07-30 15:18:32] Ejecutando en base de datos de sucursal - Data: {"codemp":"1","codsuc":"33","has_procedure":true,"has_sql":false,"test":"true","testPC":"**************"}
[2025-07-30 15:18:32] Ejecutando procedimiento en sucursal - Data: {"procedure":"SP_INSERT_AUDIT_TRANSAC_ITEMS","params":{"@CODITM":"201840,3","@CANTIDAD1":"4","@PRECIO":"22170.0","@IMPORTE":"88680.00"},"codemp":"1","codsuc":"33","test":"true","testPC":"**************"}
[2025-07-30 15:18:32] Usando conexión de prueba para procedimiento - Data: {"test":"true","testPC":"**************","procedure":"SP_INSERT_AUDIT_TRANSAC_ITEMS"}
[2025-07-30 15:18:32] Error crítico en webservice - Data: {"error":"Fallo la consulta: SQLSTATE: 42000, Code: 8114, Message: [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Error converting data type varchar to int.","trace":"#0 C:\\xampp\\htdocs\\appmovil\\api_testing\\webservice.php(116): DatabaseQueryExecutor->executeProcedure('SP_INSERT_AUDIT...', Array, '1', '33', 'true', '**************')\n#1 C:\\xampp\\htdocs\\appmovil\\api_testing\\webservice.php(273): ejecutarWebservice(Array)\n#2 {main}"}
[2025-07-30 19:41:18] Iniciando webservice - Data: {"input_data":{"codemp":"1","codsuc":"33","TEST":"true","TESTPC":"**************","proc":"SP_INSERT_AUDIT_TRANSAC","params":{"@CODCMP":"FB","@PREFIJO":"001-001","@FECHA":"2025-07-30 14:41:17","@OPERADOR":"EBARBA-Z","@TOTAL":"22170.00","@TIMESTAMP":"2025-07-30 14:41:17","@HOSTNAME":"MOVIL1-SUC33","@ACCION":"X","@LOGAPP":"{\"codigoEstado\":400,\"codigoEstadoBAS\":1012,\"cuerpo\":\"Could not find prepared statement with handle 3.\\r\\nNo se encontr\u00a2 un talonario para el prefijo '001-001' y el c\u00a2digo de POS 'MOVIL1-SUC33    '.\",\"informacionExtra\":null,\"pdf\":null,\"pdFerror\":null,\"informacionProximaConsulta\":null}","@HOOKALIAS":"SSNKG28154"}}}
[2025-07-30 19:41:18] Ejecutando en base de datos de sucursal - Data: {"codemp":"1","codsuc":"33","has_procedure":true,"has_sql":false,"test":"true","testPC":"**************"}
[2025-07-30 19:41:18] Ejecutando procedimiento en sucursal - Data: {"procedure":"SP_INSERT_AUDIT_TRANSAC","params":{"@CODCMP":"FB","@PREFIJO":"001-001","@FECHA":"2025-07-30 14:41:17","@OPERADOR":"EBARBA-Z","@TOTAL":"22170.00","@TIMESTAMP":"2025-07-30 14:41:17","@HOSTNAME":"MOVIL1-SUC33","@ACCION":"X","@LOGAPP":"{\"codigoEstado\":400,\"codigoEstadoBAS\":1012,\"cuerpo\":\"Could not find prepared statement with handle 3.\\r\\nNo se encontr\u00a2 un talonario para el prefijo '001-001' y el c\u00a2digo de POS 'MOVIL1-SUC33    '.\",\"informacionExtra\":null,\"pdf\":null,\"pdFerror\":null,\"informacionProximaConsulta\":null}","@HOOKALIAS":"SSNKG28154"},"codemp":"1","codsuc":"33","test":"true","testPC":"**************"}
[2025-07-30 19:41:18] Usando conexión de prueba para procedimiento - Data: {"test":"true","testPC":"**************","procedure":"SP_INSERT_AUDIT_TRANSAC"}
[2025-07-30 19:41:18] Error crítico en webservice - Data: {"error":"Fallo la consulta: SQLSTATE: 42000, Code: 8114, Message: [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Error converting data type varchar to int.","trace":"#0 C:\\xampp\\htdocs\\appmovil\\api_testing\\webservice.php(116): DatabaseQueryExecutor->executeProcedure('SP_INSERT_AUDIT...', Array, '1', '33', 'true', '**************')\n#1 C:\\xampp\\htdocs\\appmovil\\api_testing\\webservice.php(273): ejecutarWebservice(Array)\n#2 {main}"}
[2025-07-30 19:41:18] Iniciando webservice - Data: {"input_data":{"codemp":"1","codsuc":"33","proc":"SP_INSERT_AUDIT_TRANSAC_ITEMS","TEST":"true","TESTPC":"**************","params":{"@CODITM":"201840,0","@CANTIDAD1":"1","@PRECIO":"22170.0","@IMPORTE":"22170.00"}}}
[2025-07-30 19:41:18] Ejecutando en base de datos de sucursal - Data: {"codemp":"1","codsuc":"33","has_procedure":true,"has_sql":false,"test":"true","testPC":"**************"}
[2025-07-30 19:41:18] Ejecutando procedimiento en sucursal - Data: {"procedure":"SP_INSERT_AUDIT_TRANSAC_ITEMS","params":{"@CODITM":"201840,0","@CANTIDAD1":"1","@PRECIO":"22170.0","@IMPORTE":"22170.00"},"codemp":"1","codsuc":"33","test":"true","testPC":"**************"}
[2025-07-30 19:41:18] Usando conexión de prueba para procedimiento - Data: {"test":"true","testPC":"**************","procedure":"SP_INSERT_AUDIT_TRANSAC_ITEMS"}
[2025-07-30 19:41:18] Error crítico en webservice - Data: {"error":"Fallo la consulta: SQLSTATE: 42000, Code: 8114, Message: [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Error converting data type varchar to int.","trace":"#0 C:\\xampp\\htdocs\\appmovil\\api_testing\\webservice.php(116): DatabaseQueryExecutor->executeProcedure('SP_INSERT_AUDIT...', Array, '1', '33', 'true', '**************')\n#1 C:\\xampp\\htdocs\\appmovil\\api_testing\\webservice.php(273): ejecutarWebservice(Array)\n#2 {main}"}
[2025-07-30 20:04:51] Iniciando webservice - Data: {"input_data":{"codemp":"1","codsuc":"33","TEST":"true","TESTPC":"**************","proc":"SP_INSERT_AUDIT_TRANSAC","params":{"@CODCMP":"FB","@PREFIJO":"001-001","@FECHA":"2025-07-30 15:04:50","@OPERADOR":"Z999","@TOTAL":"44340.00","@TIMESTAMP":"2025-07-30 15:04:50","@HOSTNAME":"MOV1-SUC33","@ACCION":"X","@LOGAPP":"{\"codigoEstado\":400,\"codigoEstadoBAS\":1012,\"cuerpo\":\"El n\u00famero de cup\u00f3n manual ya fu\u00e9 utilizado.(ControlNroCupon)(SP_ICR_COMPROB_VENTA_PAGO_TARJETA)\",\"informacionExtra\":null,\"pdf\":null,\"pdFerror\":null,\"informacionProximaConsulta\":null}","@HOOKALIAS":"SQWRN47196"}}}
[2025-07-30 20:04:51] Ejecutando en base de datos de sucursal - Data: {"codemp":"1","codsuc":"33","has_procedure":true,"has_sql":false,"test":"true","testPC":"**************"}
[2025-07-30 20:04:51] Ejecutando procedimiento en sucursal - Data: {"procedure":"SP_INSERT_AUDIT_TRANSAC","params":{"@CODCMP":"FB","@PREFIJO":"001-001","@FECHA":"2025-07-30 15:04:50","@OPERADOR":"Z999","@TOTAL":"44340.00","@TIMESTAMP":"2025-07-30 15:04:50","@HOSTNAME":"MOV1-SUC33","@ACCION":"X","@LOGAPP":"{\"codigoEstado\":400,\"codigoEstadoBAS\":1012,\"cuerpo\":\"El n\u00famero de cup\u00f3n manual ya fu\u00e9 utilizado.(ControlNroCupon)(SP_ICR_COMPROB_VENTA_PAGO_TARJETA)\",\"informacionExtra\":null,\"pdf\":null,\"pdFerror\":null,\"informacionProximaConsulta\":null}","@HOOKALIAS":"SQWRN47196"},"codemp":"1","codsuc":"33","test":"true","testPC":"**************"}
[2025-07-30 20:04:51] Usando conexión de prueba para procedimiento - Data: {"test":"true","testPC":"**************","procedure":"SP_INSERT_AUDIT_TRANSAC"}
[2025-07-30 20:04:51] Error crítico en webservice - Data: {"error":"Fallo la consulta: SQLSTATE: 42000, Code: 8114, Message: [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Error converting data type varchar to int.","trace":"#0 C:\\xampp\\htdocs\\appmovil\\api_testing\\webservice.php(116): DatabaseQueryExecutor->executeProcedure('SP_INSERT_AUDIT...', Array, '1', '33', 'true', '**************')\n#1 C:\\xampp\\htdocs\\appmovil\\api_testing\\webservice.php(273): ejecutarWebservice(Array)\n#2 {main}"}
[2025-07-30 20:04:51] Iniciando webservice - Data: {"input_data":{"codemp":"1","codsuc":"33","proc":"SP_INSERT_AUDIT_TRANSAC_ITEMS","TEST":"true","TESTPC":"**************","params":{"@CODITM":"201840,0","@CANTIDAD1":"2","@PRECIO":"22170.0","@IMPORTE":"44340.00"}}}
[2025-07-30 20:04:51] Ejecutando en base de datos de sucursal - Data: {"codemp":"1","codsuc":"33","has_procedure":true,"has_sql":false,"test":"true","testPC":"**************"}
[2025-07-30 20:04:51] Ejecutando procedimiento en sucursal - Data: {"procedure":"SP_INSERT_AUDIT_TRANSAC_ITEMS","params":{"@CODITM":"201840,0","@CANTIDAD1":"2","@PRECIO":"22170.0","@IMPORTE":"44340.00"},"codemp":"1","codsuc":"33","test":"true","testPC":"**************"}
[2025-07-30 20:04:51] Usando conexión de prueba para procedimiento - Data: {"test":"true","testPC":"**************","procedure":"SP_INSERT_AUDIT_TRANSAC_ITEMS"}
[2025-07-30 20:04:51] Error crítico en webservice - Data: {"error":"Fallo la consulta: SQLSTATE: 42000, Code: 8114, Message: [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Error converting data type varchar to int.","trace":"#0 C:\\xampp\\htdocs\\appmovil\\api_testing\\webservice.php(116): DatabaseQueryExecutor->executeProcedure('SP_INSERT_AUDIT...', Array, '1', '33', 'true', '**************')\n#1 C:\\xampp\\htdocs\\appmovil\\api_testing\\webservice.php(273): ejecutarWebservice(Array)\n#2 {main}"}
