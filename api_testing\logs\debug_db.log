[2025-06-20 15:37:56] Procedimient=== Nueva Sesión Iniciada: 2025-06-25 19:23:47 ===
[2025-06-25 19:23:47] Fallo el procedimiento - Data: "SQLSTATE: 42000, Code: 8144, Message: [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]La funci\u00f3n o el procedimiento SP_PUT_ALIAS_TBANCARD tiene demasiados argumentos."
[2025-06-25 19:23:47] Error de base de datos: Fallo la consulta: SQLSTATE: 42000, Code: 8144, Message: [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]La función o el procedimiento SP_PUT_ALIAS_TBANCARD tiene demasiados argumentos.
[2025-06-25 19:23:47] Error al ejecutar el procedimiento SP_PUT_ALIAS_TBANCARD - Data: {"hookalias":"SAKFO90257","amount":"438900","description":"<PERSON><PERSON><PERSON> Guardar alias","created_at":"25/06/2025 12:00:00"}
=== Nueva Sesión Iniciada: 2025-06-25 19:30:35 ===
[2025-06-25 19:30:35] Fallo el procedimiento (dev) - Data: "SQLSTATE: 42000, Code: 8144, Message: [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]La funci\u00f3n o el procedimiento SP_PUT_ALIAS_TBANCARD tiene demasiados argumentos."
[2025-06-25 19:30:35] Error al ejecutar el procedimiento SP_PUT_ALIAS_TBANCARD - Data: {"hookalias":"SAKFO90257","amount":"438900","description":"Prueba Guardar alias","created_at":"25/06/2025 12:00:00"}
=== Nueva Sesión Iniciada: 2025-06-25 19:30:36 ===
[2025-06-25 19:30:36] Fallo el procedimiento (dev) - Data: "SQLSTATE: 42000, Code: 8144, Message: [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]La funci\u00f3n o el procedimiento SP_PUT_ALIAS_TBANCARD tiene demasiados argumentos."
[2025-06-25 19:30:36] Error al ejecutar el procedimiento SP_PUT_ALIAS_TBANCARD - Data: {"hookalias":"SAKFO90257","amount":"438900","description":"Prueba Guardar alias","created_at":"25/06/2025 12:00:00"}
=== Nueva Sesión Iniciada: 2025-06-25 19:35:17 ===
[2025-06-25 19:35:17] Fallo el procedimiento (dev) - Data: "SQLSTATE: 42000, Code: 201, Message: [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]El procedimiento o la funci\u00f3n 'SP_PUT_ALIAS_TBANCARD' esperaba el par\u00e1metro '@created_at', que no se ha especificado."
[2025-06-25 19:35:17] Error al ejecutar el procedimiento SP_PUT_ALIAS_TBANCARD - Data: {"hookalias":"SAKFO90257","description":"Prueba Guardar alias","created_at":"25/06/2025 12:00:00"}
=== Nueva Sesión Iniciada: 2025-06-25 19:35:48 ===
[2025-06-25 19:35:48] Fallo el procedimiento (dev) - Data: "SQLSTATE: 42000, Code: 50000, Message: [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]La fecha proporcionada no tiene un formato v\u00e1lido. Use yyyy-mm-dd hh:mi:ss"
[2025-06-25 19:35:48] Error al ejecutar el procedimiento SP_PUT_ALIAS_TBANCARD - Data: {"hookalias":"SAKFO90257","amount":"438900","description":"Prueba Guardar alias","created_at":"25/06/2025 12:00:00"}
=== Nueva Sesión Iniciada: 2025-06-25 19:36:44 ===
[2025-06-25 19:36:44] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_PUT_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-25 19:39:30 ===
[2025-06-25 19:39:30] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 19:44:12 ===
[2025-06-25 19:44:12] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_PUT_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-25 19:44:12 ===
[2025-06-25 19:44:12] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 19:44:17 ===
[2025-06-25 19:44:17] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 19:44:22 ===
[2025-06-25 19:44:22] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 19:44:27 ===
[2025-06-25 19:44:27] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 19:44:32 ===
[2025-06-25 19:44:32] Fallo el procedimiento - Data: "SQLSTATE: 42000, Code: 2812, Message: [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]No se encontr\u00f3 el procedimiento almacenado 'SP_PUT_DATOS_TBANCARD'."
[2025-06-25 19:44:32] Error de base de datos: Fallo la consulta: SQLSTATE: 42000, Code: 2812, Message: [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]No se encontró el procedimiento almacenado 'SP_PUT_DATOS_TBANCARD'.
[2025-06-25 19:44:32] Error al ejecutar procedimiento con datos: {"hookalias":"SBUYW70643","status":"confirmed","response_code":"00","response_description":"Pago Exitoso","amount":678300,"currency":"GS","installment_number":1,"product_description":"Compra en Tienda de Nike","date_time":"25\/06\/2025 16:44:30","ticket_number":**********,"authorization_code":"868419","commerce_name":"ZAVIDORO CORPORATIONS SUC.PY.","branch_name":"NIKE-SOL","bin":"************","merchant_code":"553149","payer_name":null,"payer_lastname":null,"card_last_numbers":"2045","account_type":"TC"}
[2025-06-25 19:44:32] HTTP 500 Error: Server error occurred
=== Nueva Sesión Iniciada: 2025-06-25 19:44:32 ===
[2025-06-25 19:44:32] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 19:44:37 ===
[2025-06-25 19:44:37] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 19:44:42 ===
[2025-06-25 19:44:42] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 19:44:47 ===
[2025-06-25 19:44:47] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 19:44:52 ===
[2025-06-25 19:44:52] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 19:44:57 ===
[2025-06-25 19:44:57] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 19:45:02 ===
[2025-06-25 19:45:02] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 19:45:07 ===
[2025-06-25 19:45:07] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 19:45:12 ===
[2025-06-25 19:45:12] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 19:48:30 ===
[2025-06-25 19:48:30] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_PUT_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-25 19:49:06 ===
=== Nueva Sesión Iniciada: 2025-06-25 19:49:06 ===
[2025-06-25 19:49:06] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
[2025-06-25 19:49:06] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_PUT_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-25 19:49:11 ===
[2025-06-25 19:49:11] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 19:49:16 ===
[2025-06-25 19:49:16] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 19:49:21 ===
[2025-06-25 19:49:21] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 19:49:26 ===
[2025-06-25 19:49:26] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 19:49:31 ===
[2025-06-25 19:49:31] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 19:49:32 ===
[2025-06-25 19:49:32] Fallo el procedimiento - Data: "SQLSTATE: 42000, Code: 2812, Message: [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]No se encontr\u00f3 el procedimiento almacenado 'SP_PUT_DATOS_TBANCARD'."
[2025-06-25 19:49:32] Error de base de datos: Fallo la consulta: SQLSTATE: 42000, Code: 2812, Message: [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]No se encontró el procedimiento almacenado 'SP_PUT_DATOS_TBANCARD'.
[2025-06-25 19:49:32] Error al ejecutar procedimiento con datos: {"hookalias":"SLWFU69057","status":"confirmed","response_code":"00","response_description":"Pago Exitoso","amount":678300,"currency":"GS","installment_number":1,"product_description":"Compra en Tienda de Nike","date_time":"25\/06\/2025 16:49:30","ticket_number":**********,"authorization_code":"868420","commerce_name":"ZAVIDORO CORPORATIONS SUC.PY.","branch_name":"NIKE-SOL","bin":"************","merchant_code":"553149","payer_name":null,"payer_lastname":null,"card_last_numbers":"2045","account_type":"TC"}
[2025-06-25 19:49:32] HTTP 500 Error: Server error occurred
=== Nueva Sesión Iniciada: 2025-06-25 19:49:36 ===
[2025-06-25 19:49:36] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 19:49:41 ===
[2025-06-25 19:49:41] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 19:49:46 ===
[2025-06-25 19:49:46] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 19:50:16 ===
[2025-06-25 19:50:16] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 19:50:58 ===
[2025-06-25 19:50:58] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 19:56:00 ===
[2025-06-25 19:56:00] Fallo el procedimiento - Data: "SQLSTATE: 42000, Code: 201, Message: [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]El procedimiento o la funci\u00f3n 'SP_PUT_DATOS_TBANCARD' esperaba el par\u00e1metro '@created_at', que no se ha especificado."
[2025-06-25 19:56:00] Error de base de datos: Fallo la consulta: SQLSTATE: 42000, Code: 201, Message: [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]El procedimiento o la función 'SP_PUT_DATOS_TBANCARD' esperaba el parámetro '@created_at', que no se ha especificado.
[2025-06-25 19:56:00] Error al ejecutar procedimiento con datos: {"hookalias":"SAKFO90257","status":"confirmed","response_code":"00","response_description":"Pago exitoso","amount":438900,"currency":"GS","installment_number":10,"product_description":"Coca Cola 1 Ltr.","date_time":"31\/10\/2019 13:59:39","ticket_number":**************,"authorization_code":"134243","commerce_name":"Supermercado Maravilla","branch_name":"Sucursal San Vicente","bin":"433234","merchant_code":"51111","payer_name":null,"payer_lastname":null,"card_last_numbers":1234,"account_type":"TC"}
[2025-06-25 19:56:00] HTTP 500 Error: Server error occurred
=== Nueva Sesión Iniciada: 2025-06-25 19:56:28 ===
[2025-06-25 19:56:28] Fallo el procedimiento - Data: "SQLSTATE: 42000, Code: 201, Message: [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]El procedimiento o la funci\u00f3n 'SP_PUT_DATOS_TBANCARD' esperaba el par\u00e1metro '@created_at', que no se ha especificado."
[2025-06-25 19:56:28] Error de base de datos: Fallo la consulta: SQLSTATE: 42000, Code: 201, Message: [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]El procedimiento o la función 'SP_PUT_DATOS_TBANCARD' esperaba el parámetro '@created_at', que no se ha especificado.
[2025-06-25 19:56:28] Error al ejecutar procedimiento con datos: {"hookalias":"SAKFO90257","status":"confirmed","response_code":"00","response_description":"Pago exitoso","amount":438900,"currency":"GS","installment_number":10,"product_description":"Coca Cola 1 Ltr.","date_time":"31\/10\/2019 13:59:39","ticket_number":**************,"authorization_code":"134243","commerce_name":"Supermercado Maravilla","branch_name":"Sucursal San Vicente","bin":"433234","merchant_code":"51111","payer_name":null,"payer_lastname":null,"card_last_numbers":1234,"account_type":"TC"}
[2025-06-25 19:56:28] HTTP 500 Error: Server error occurred
=== Nueva Sesión Iniciada: 2025-06-25 19:58:35 ===
[2025-06-25 19:58:35] Fallo el procedimiento - Data: "SQLSTATE: 42000, Code: 50000, Message: [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]El campo created_at no tiene un formato v\u00c3\u00a1lido."
[2025-06-25 19:58:35] Error de base de datos: Fallo la consulta: SQLSTATE: 42000, Code: 50000, Message: [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]El campo created_at no tiene un formato vÃ¡lido.
[2025-06-25 19:58:35] Error al ejecutar procedimiento con datos: {"hookalias":"SAKFO90257","status":"confirmed","response_code":"00","response_description":"Pago exitoso","amount":438900,"currency":"GS","installment_number":10,"product_description":"Coca Cola 1 Ltr.","date_time":"31\/10\/2019 13:59:39","ticket_number":**************,"authorization_code":"134243","commerce_name":"Supermercado Maravilla","branch_name":"Sucursal San Vicente","bin":"433234","merchant_code":"51111","payer_name":null,"payer_lastname":null,"card_last_numbers":1234,"account_type":"TC","created_at":"2020-05-20T20:25:00.000Z"}
[2025-06-25 19:58:35] HTTP 500 Error: Server error occurred
=== Nueva Sesión Iniciada: 2025-06-25 19:59:33 ===
[2025-06-25 19:59:33] Pago exitoso para hook_alias: "SAKFO90257"
=== Nueva Sesión Iniciada: 2025-06-25 20:02:11 ===
[2025-06-25 20:02:11] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_PUT_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-25 20:02:11 ===
[2025-06-25 20:02:11] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 20:02:16 ===
[2025-06-25 20:02:16] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 20:02:21 ===
[2025-06-25 20:02:21] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 20:02:23 ===
[2025-06-25 20:02:23] Pago exitoso para hook_alias: "SNJVQ45193"
=== Nueva Sesión Iniciada: 2025-06-25 20:02:26 ===
[2025-06-25 20:02:26] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 20:13:01 ===
[2025-06-25 20:13:01] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_PUT_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-25 20:13:02 ===
[2025-06-25 20:13:02] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 20:13:07 ===
[2025-06-25 20:13:07] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 20:13:12 ===
[2025-06-25 20:13:12] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 20:13:17 ===
[2025-06-25 20:13:17] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 20:13:22 ===
[2025-06-25 20:13:22] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 20:13:27 ===
[2025-06-25 20:13:27] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 20:13:32 ===
[2025-06-25 20:13:32] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 20:13:37 ===
[2025-06-25 20:13:37] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 20:13:39 ===
[2025-06-25 20:13:39] Se recibio un status diferente al confirmed: failed
=== Nueva Sesión Iniciada: 2025-06-25 20:13:42 ===
[2025-06-25 20:13:42] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 20:13:47 ===
[2025-06-25 20:13:47] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 20:13:52 ===
[2025-06-25 20:13:52] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 20:13:57 ===
[2025-06-25 20:13:57] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 20:14:02 ===
[2025-06-25 20:14:02] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 20:16:23 ===
[2025-06-25 20:16:23] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_PUT_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-25 20:16:23 ===
[2025-06-25 20:16:23] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 20:16:28 ===
[2025-06-25 20:16:28] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 20:16:33 ===
[2025-06-25 20:16:33] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 20:16:34 ===
[2025-06-25 20:16:34] Pago exitoso para hook_alias: "SQMZO65701"
=== Nueva Sesión Iniciada: 2025-06-25 20:16:38 ===
[2025-06-25 20:16:38] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 20:21:03 ===
[2025-06-25 20:21:03] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_PUT_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-25 20:21:04 ===
[2025-06-25 20:21:04] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 20:21:09 ===
[2025-06-25 20:21:09] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 20:21:14 ===
[2025-06-25 20:21:14] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 20:21:19 ===
[2025-06-25 20:21:19] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 20:21:24 ===
[2025-06-25 20:21:24] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 20:21:29 ===
[2025-06-25 20:21:29] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 20:21:34 ===
[2025-06-25 20:21:34] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 20:21:39 ===
[2025-06-25 20:21:39] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 20:21:44 ===
[2025-06-25 20:21:44] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 20:21:49 ===
[2025-06-25 20:21:49] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 20:21:54 ===
[2025-06-25 20:21:54] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 20:21:59 ===
[2025-06-25 20:21:59] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 20:22:04 ===
[2025-06-25 20:22:04] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 20:22:09 ===
[2025-06-25 20:22:09] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 20:22:14 ===
[2025-06-25 20:22:14] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 20:22:19 ===
[2025-06-25 20:22:19] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 20:22:24 ===
[2025-06-25 20:22:24] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 20:22:29 ===
[2025-06-25 20:22:29] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 20:29:04 ===
[2025-06-25 20:29:04] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_PUT_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-25 20:29:04 ===
[2025-06-25 20:29:04] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 20:29:09 ===
[2025-06-25 20:29:09] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 20:29:14 ===
[2025-06-25 20:29:14] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 20:29:19 ===
[2025-06-25 20:29:19] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 20:29:24 ===
[2025-06-25 20:29:24] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 20:29:29 ===
[2025-06-25 20:29:29] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 20:29:34 ===
[2025-06-25 20:29:34] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 20:29:39 ===
[2025-06-25 20:29:39] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 20:29:44 ===
[2025-06-25 20:29:44] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 20:29:50 ===
[2025-06-25 20:29:50] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 20:29:54 ===
[2025-06-25 20:29:54] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 20:29:59 ===
[2025-06-25 20:29:59] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 20:30:04 ===
[2025-06-25 20:30:04] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 20:30:09 ===
[2025-06-25 20:30:09] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 20:30:14 ===
[2025-06-25 20:30:14] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 20:30:19 ===
[2025-06-25 20:30:19] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 20:30:24 ===
[2025-06-25 20:30:24] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 20:30:29 ===
[2025-06-25 20:30:29] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 20:30:34 ===
[2025-06-25 20:30:34] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 20:30:39 ===
[2025-06-25 20:30:39] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 20:30:44 ===
[2025-06-25 20:30:44] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 20:30:49 ===
[2025-06-25 20:30:49] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 20:30:54 ===
[2025-06-25 20:30:54] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 20:30:59 ===
[2025-06-25 20:30:59] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 20:33:39 ===
[2025-06-25 20:33:39] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_PUT_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-25 20:33:40 ===
[2025-06-25 20:33:40] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_PUT_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-25 20:33:40 ===
[2025-06-25 20:33:40] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 20:33:45 ===
[2025-06-25 20:33:45] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-25 20:33:45 ===
[2025-06-25 20:33:45] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_PUT_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-25 20:33:46 ===
[2025-06-25 20:33:46] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
[2025-06-26 08:39:19] Procedimiento ejecutado exitosamente (Sucursales) - Data: {"procedure":"SP_GET_COMP_HEAD_NOTIF","server":"srv-suc03.swoosh.net","database":"SUC03","results_count":1}
[2025-06-26 08:39:19] Procedimiento ejecutado exitosamente (Sucursales) - Data: {"procedure":"SP_GET_COMP_DET_NOTIF","server":"srv-suc03.swoosh.net","database":"SUC03","results_count":5}
[2025-06-26 08:49:31] Procedimiento ejecutado exitosamente (Sucursales) - Data: {"procedure":"SP_GET_COMP_HEAD_NOTIF","server":"srv-suc03.swoosh.net","database":"SUC03","results_count":1}
[2025-06-26 08:56:49] Procedimiento ejecutado exitosamente (Sucursales) - Data: {"procedure":"SP_GET_COMP_HEAD_NOTIF","server":"srv-suc03.swoosh.net","database":"SUC03","results_count":1}
[2025-06-26 08:56:49] Procedimiento ejecutado exitosamente (Sucursales) - Data: {"procedure":"SP_GET_COMP_DET_NOTIF","server":"srv-suc03.swoosh.net","database":"SUC03","results_count":5}
[2025-06-26 09:00:46] Procedimiento ejecutado exitosamente (Sucursales) - Data: {"procedure":"SP_GET_COMP_HEAD_NOTIF","server":"srv-suc03.swoosh.net","database":"SUC03","results_count":1}
[2025-06-26 09:00:46] Procedimiento ejecutado exitosamente (Sucursales) - Data: {"procedure":"SP_GET_COMP_DET_NOTIF","server":"srv-suc03.swoosh.net","database":"SUC03","results_count":5}
=== Nueva Sesión Iniciada: 2025-06-26 12:08:44 ===
[2025-06-26 12:08:44] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_PUT_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-26 12:08:45 ===
[2025-06-26 12:08:45] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 12:08:50 ===
[2025-06-26 12:08:50] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 12:08:55 ===
[2025-06-26 12:08:55] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 12:08:58 ===
[2025-06-26 12:08:58] Pago exitoso para hook_alias: "SXAQF03158"
=== Nueva Sesión Iniciada: 2025-06-26 12:09:00 ===
[2025-06-26 12:09:00] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 12:12:55 ===
[2025-06-26 12:12:55] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_PUT_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-26 12:12:55 ===
[2025-06-26 12:12:55] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 12:13:00 ===
[2025-06-26 12:13:00] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 12:13:05 ===
[2025-06-26 12:13:05] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 12:13:07 ===
[2025-06-26 12:13:07] Pago exitoso para hook_alias: "SQING15268"
=== Nueva Sesión Iniciada: 2025-06-26 12:13:10 ===
[2025-06-26 12:13:10] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 12:21:51 ===
[2025-06-26 12:21:51] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_PUT_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-26 12:21:51 ===
[2025-06-26 12:21:51] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 12:21:56 ===
[2025-06-26 12:21:56] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 12:22:01 ===
[2025-06-26 12:22:01] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 12:22:07 ===
[2025-06-26 12:22:07] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 12:22:11 ===
[2025-06-26 12:22:11] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 12:22:16 ===
[2025-06-26 12:22:16] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 12:22:21 ===
[2025-06-26 12:22:21] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 12:22:24 ===
[2025-06-26 12:22:24] Pago exitoso para hook_alias: "SLRIX80321"
=== Nueva Sesión Iniciada: 2025-06-26 12:22:26 ===
[2025-06-26 12:22:26] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 12:30:16 ===
[2025-06-26 12:30:16] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_PUT_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-26 12:30:17 ===
[2025-06-26 12:30:17] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 12:30:22 ===
[2025-06-26 12:30:22] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 12:30:27 ===
[2025-06-26 12:30:27] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 12:30:27 ===
[2025-06-26 12:30:27] Pago exitoso para hook_alias: "SKEVC89135"
=== Nueva Sesión Iniciada: 2025-06-26 12:30:32 ===
[2025-06-26 12:30:32] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 12:33:24 ===
[2025-06-26 12:33:24] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_PUT_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-26 12:33:25 ===
[2025-06-26 12:33:25] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 12:33:30 ===
[2025-06-26 12:33:30] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 12:33:35 ===
[2025-06-26 12:33:35] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 12:33:35 ===
[2025-06-26 12:33:36] Se recibio un status diferente al confirmed: failed
=== Nueva Sesión Iniciada: 2025-06-26 12:33:40 ===
[2025-06-26 12:33:40] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 12:33:45 ===
[2025-06-26 12:33:45] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 12:33:50 ===
[2025-06-26 12:33:50] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
[2025-06-26 09:54:17] Procedimiento ejecutado exitosamente (Sucursales) - Data: {"procedure":"SP_GET_COMP_HEAD_NOTIF","server":"srv-suc03.swoosh.net","database":"SUC03","results_count":1}
[2025-06-26 09:54:17] Procedimiento ejecutado exitosamente (Sucursales) - Data: {"procedure":"SP_GET_COMP_DET_NOTIF","server":"srv-suc03.swoosh.net","database":"SUC03","results_count":5}
[2025-06-26 09:57:05] Procedimiento ejecutado exitosamente (Sucursales) - Data: {"procedure":"SP_GET_COMP_HEAD_NOTIF","server":"srv-suc03.swoosh.net","database":"SUC03","results_count":1}
[2025-06-26 09:57:05] Procedimiento ejecutado exitosamente (Sucursales) - Data: {"procedure":"SP_GET_COMP_DET_NOTIF","server":"srv-suc03.swoosh.net","database":"SUC03","results_count":5}
[2025-06-26 09:58:44] Procedimiento ejecutado exitosamente (Sucursales) - Data: {"procedure":"SP_GET_COMP_HEAD_NOTIF","server":"srv-suc03.swoosh.net","database":"SUC03","results_count":1}
[2025-06-26 09:58:44] Procedimiento ejecutado exitosamente (Sucursales) - Data: {"procedure":"SP_GET_COMP_DET_NOTIF","server":"srv-suc03.swoosh.net","database":"SUC03","results_count":5}
[2025-06-26 10:03:35] Procedimiento ejecutado exitosamente (Sucursales) - Data: {"procedure":"SP_GET_COMP_HEAD_NOTIF","server":"srv-suc03.swoosh.net","database":"SUC03","results_count":1}
[2025-06-26 10:03:35] Procedimiento ejecutado exitosamente (Sucursales) - Data: {"procedure":"SP_GET_COMP_DET_NOTIF","server":"srv-suc03.swoosh.net","database":"SUC03","results_count":5}
=== Nueva Sesión Iniciada: 2025-06-26 13:03:43 ===
[2025-06-26 13:03:43] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 13:12:45 ===
[2025-06-26 13:12:45] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 13:14:03 ===
[2025-06-26 13:14:03] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 13:38:27 ===
[2025-06-26 13:38:27] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 13:42:57 ===
[2025-06-26 13:42:57] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 13:53:38 ===
[2025-06-26 13:53:38] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 13:58:47 ===
[2025-06-26 13:58:47] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 14:00:57 ===
[2025-06-26 14:00:57] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
[2025-06-26 11:02:08] Procedimiento ejecutado exitosamente (Sucursales) - Data: {"procedure":"SP_GET_COMP_HEAD_NOTIF","server":"srv-suc03.swoosh.net","database":"SUC03","results_count":1}
[2025-06-26 11:02:08] Procedimiento ejecutado exitosamente (Sucursales) - Data: {"procedure":"SP_GET_COMP_DET_NOTIF","server":"srv-suc03.swoosh.net","database":"SUC03","results_count":5}
=== Nueva Sesión Iniciada: 2025-06-26 14:13:24 ===
[2025-06-26 14:13:24] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 14:13:29 ===
[2025-06-26 14:13:29] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 14:13:30 ===
[2025-06-26 14:13:30] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 14:16:43 ===
[2025-06-26 14:16:43] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_PUT_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-26 14:16:44 ===
[2025-06-26 14:16:44] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 14:16:48 ===
[2025-06-26 14:16:48] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_PUT_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-26 14:16:48 ===
[2025-06-26 14:16:48] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 14:16:50 ===
[2025-06-26 14:16:50] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_PUT_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-26 14:16:50 ===
[2025-06-26 14:16:50] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 14:16:55 ===
[2025-06-26 14:16:55] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_PUT_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-26 14:16:55 ===
[2025-06-26 14:16:55] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 14:17:00 ===
[2025-06-26 14:17:00] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 14:17:05 ===
[2025-06-26 14:17:05] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 14:17:10 ===
[2025-06-26 14:17:10] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 14:17:15 ===
[2025-06-26 14:17:15] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 14:17:20 ===
[2025-06-26 14:17:20] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 14:17:25 ===
[2025-06-26 14:17:25] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 14:17:30 ===
[2025-06-26 14:17:30] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 14:17:35 ===
[2025-06-26 14:17:35] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 14:17:40 ===
[2025-06-26 14:17:40] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 14:17:45 ===
[2025-06-26 14:17:45] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 14:17:50 ===
[2025-06-26 14:17:50] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 14:17:55 ===
[2025-06-26 14:17:55] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 14:18:00 ===
[2025-06-26 14:18:00] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 14:18:05 ===
[2025-06-26 14:18:05] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 14:18:10 ===
[2025-06-26 14:18:10] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 14:18:15 ===
[2025-06-26 14:18:15] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 14:18:20 ===
[2025-06-26 14:18:20] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 14:18:25 ===
[2025-06-26 14:18:25] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 14:18:30 ===
[2025-06-26 14:18:30] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 14:18:35 ===
[2025-06-26 14:18:35] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 14:18:40 ===
[2025-06-26 14:18:40] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 14:28:17 ===
[2025-06-26 14:28:17] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_PUT_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-26 14:28:18 ===
[2025-06-26 14:28:18] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 14:29:24 ===
[2025-06-26 14:29:24] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_PUT_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-26 14:29:24 ===
[2025-06-26 14:29:24] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 14:29:29 ===
[2025-06-26 14:29:29] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 14:29:34 ===
[2025-06-26 14:29:34] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 14:29:39 ===
[2025-06-26 14:29:39] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 14:29:44 ===
[2025-06-26 14:29:44] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 14:47:03 ===
[2025-06-26 14:47:03] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_PUT_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-26 14:47:04 ===
[2025-06-26 14:47:04] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 14:47:09 ===
[2025-06-26 14:47:09] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 14:47:14 ===
[2025-06-26 14:47:14] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 14:47:19 ===
[2025-06-26 14:47:19] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 14:50:39 ===
[2025-06-26 14:50:39] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_PUT_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-26 14:50:40 ===
[2025-06-26 14:50:40] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 14:50:45 ===
[2025-06-26 14:50:45] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 14:50:50 ===
[2025-06-26 14:50:50] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 15:13:02 ===
[2025-06-26 15:13:02] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_PUT_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-26 15:13:02 ===
[2025-06-26 15:13:02] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 15:13:07 ===
[2025-06-26 15:13:07] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 15:13:12 ===
[2025-06-26 15:13:12] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 15:13:27 ===
[2025-06-26 15:13:27] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_PUT_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-26 15:13:27 ===
[2025-06-26 15:13:27] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 15:13:32 ===
[2025-06-26 15:13:32] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 15:13:37 ===
[2025-06-26 15:13:37] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 15:13:42 ===
[2025-06-26 15:13:42] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 15:13:47 ===
[2025-06-26 15:13:47] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 15:13:52 ===
[2025-06-26 15:13:52] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 15:13:57 ===
[2025-06-26 15:13:57] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 15:14:02 ===
[2025-06-26 15:14:02] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 15:14:07 ===
[2025-06-26 15:14:07] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 15:14:12 ===
[2025-06-26 15:14:12] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 15:14:17 ===
[2025-06-26 15:14:17] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 15:14:22 ===
[2025-06-26 15:14:22] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 15:14:27 ===
[2025-06-26 15:14:27] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 15:14:32 ===
[2025-06-26 15:14:32] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 15:14:37 ===
[2025-06-26 15:14:37] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 15:14:42 ===
[2025-06-26 15:14:42] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 15:14:47 ===
[2025-06-26 15:14:47] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 15:14:52 ===
[2025-06-26 15:14:52] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 15:14:57 ===
[2025-06-26 15:14:58] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 15:15:02 ===
[2025-06-26 15:15:03] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 15:15:07 ===
[2025-06-26 15:15:08] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 15:15:12 ===
[2025-06-26 15:15:13] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 15:15:18 ===
[2025-06-26 15:15:18] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 15:15:23 ===
[2025-06-26 15:15:23] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 17:09:13 ===
[2025-06-26 17:09:13] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_PUT_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-26 17:09:13 ===
[2025-06-26 17:09:13] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 17:09:18 ===
[2025-06-26 17:09:18] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 17:09:23 ===
[2025-06-26 17:09:23] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 17:09:28 ===
[2025-06-26 17:09:28] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 17:09:33 ===
[2025-06-26 17:09:33] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
[2025-06-26 14:09:38] Procedimiento ejecutado exitosamente (Sucursales) - Data: {"procedure":"SP_GET_COMP_HEAD_NOTIF","server":"srv-suc03.swoosh.net","database":"SUC03","results_count":1}
[2025-06-26 14:09:39] Procedimiento ejecutado exitosamente (Sucursales) - Data: {"procedure":"SP_GET_COMP_DET_NOTIF","server":"srv-suc03.swoosh.net","database":"SUC03","results_count":2}
=== Nueva Sesión Iniciada: 2025-06-26 17:14:10 ===
[2025-06-26 17:14:10] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_PUT_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-26 17:14:10 ===
[2025-06-26 17:14:10] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 17:14:15 ===
[2025-06-26 17:14:15] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 17:14:20 ===
[2025-06-26 17:14:20] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
[2025-06-26 14:14:21] Procedimiento ejecutado exitosamente (Sucursales) - Data: {"procedure":"SP_GET_COMP_HEAD_NOTIF","server":"srv-suc03.swoosh.net","database":"SUC03","results_count":1}
[2025-06-26 14:14:21] Procedimiento ejecutado exitosamente (Sucursales) - Data: {"procedure":"SP_GET_COMP_DET_NOTIF","server":"srv-suc03.swoosh.net","database":"SUC03","results_count":2}
=== Nueva Sesión Iniciada: 2025-06-26 17:18:29 ===
[2025-06-26 17:18:29] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_PUT_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-26 17:18:30 ===
[2025-06-26 17:18:30] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 17:18:35 ===
[2025-06-26 17:18:35] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 17:18:40 ===
[2025-06-26 17:18:40] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 17:18:51 ===
[2025-06-26 17:18:51] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_PUT_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-26 17:18:51 ===
[2025-06-26 17:18:51] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 17:18:56 ===
[2025-06-26 17:18:56] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 17:19:01 ===
[2025-06-26 17:19:01] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 17:20:37 ===
[2025-06-26 17:20:37] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_PUT_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-26 17:20:37 ===
[2025-06-26 17:20:37] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 17:20:42 ===
[2025-06-26 17:20:42] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 17:20:47 ===
[2025-06-26 17:20:47] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
=== Nueva Sesión Iniciada: 2025-06-26 17:20:52 ===
[2025-06-26 17:20:52] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":1}
[2025-06-26 14:20:54] Procedimiento ejecutado exitosamente (Sucursales) - Data: {"procedure":"SP_GET_COMP_HEAD_NOTIF","server":"srv-suc03.swoosh.net","database":"SUC03","results_count":1}
[2025-06-26 14:20:54] Procedimiento ejecutado exitosamente (Sucursales) - Data: {"procedure":"SP_GET_COMP_DET_NOTIF","server":"srv-suc03.swoosh.net","database":"SUC03","results_count":2}
=== Nueva Sesión Iniciada: 2025-06-26 17:46:31 ===
[2025-06-26 17:46:32] Fallo el procedimiento - Data: "SQLSTATE: 42000, Code: 8144, Message: [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]La funci\u00f3n o el procedimiento SP_PUT_DATOS_TBANCARD tiene demasiados argumentos."
[2025-06-26 17:46:32] Error de base de datos: Fallo la consulta: SQLSTATE: 42000, Code: 8144, Message: [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]La función o el procedimiento SP_PUT_DATOS_TBANCARD tiene demasiados argumentos.
[2025-06-26 17:46:32] Error al ejecutar procedimiento con datos: {"hookalias":"SAKFO90257","status":"confirmed","response_code":"00","response_description":"Pago exitoso","amount":438900,"currency":"GS","installment_number":10,"product_description":"Coca Cola 1 Ltr.","date_time":"31\/10\/2019 13:59:39","ticket_number":**************,"authorization_code":"134243","commerce_name":"Supermercado Maravilla","branch_name":"Sucursal San Vicente","bin":"433234","merchant_code":"51111","payer_name":null,"payer_lastname":null,"card_last_numbers":1234,"account_type":"TC","created_at":"2020-05-20T20:25:00.000Z"}
[2025-06-26 17:46:32] HTTP 500 Error: Server error occurred
=== Nueva Sesión Iniciada: 2025-06-26 17:46:49 ===
[2025-06-26 17:46:49] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_PUT_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-26 17:46:56 ===
[2025-06-26 17:46:56] Fallo el procedimiento - Data: "SQLSTATE: 42000, Code: 8144, Message: [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]La funci\u00f3n o el procedimiento SP_PUT_DATOS_TBANCARD tiene demasiados argumentos."
[2025-06-26 17:46:56] Error de base de datos: Fallo la consulta: SQLSTATE: 42000, Code: 8144, Message: [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]La función o el procedimiento SP_PUT_DATOS_TBANCARD tiene demasiados argumentos.
[2025-06-26 17:46:56] Error al ejecutar procedimiento con datos: {"hookalias":"SAKFO9999","status":"confirmed","response_code":"00","response_description":"Pago exitoso","amount":438900,"currency":"GS","installment_number":10,"product_description":"Coca Cola 1 Ltr.","date_time":"31\/10\/2019 13:59:39","ticket_number":**************,"authorization_code":"134243","commerce_name":"Supermercado Maravilla","branch_name":"Sucursal San Vicente","bin":"433234","merchant_code":"51111","payer_name":null,"payer_lastname":null,"card_last_numbers":1234,"account_type":"TC","created_at":"2020-05-20T20:25:00.000Z"}
[2025-06-26 17:46:56] HTTP 500 Error: Server error occurred
=== Nueva Sesión Iniciada: 2025-06-26 17:52:19 ===
[2025-06-26 17:52:19] Fallo el procedimiento - Data: "SQLSTATE: 42000, Code: 8144, Message: [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]La funci\u00f3n o el procedimiento SP_PUT_DATOS_TBANCARD tiene demasiados argumentos."
[2025-06-26 17:52:19] Error de base de datos: Fallo la consulta: SQLSTATE: 42000, Code: 8144, Message: [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]La función o el procedimiento SP_PUT_DATOS_TBANCARD tiene demasiados argumentos.
[2025-06-26 17:52:19] Error al ejecutar procedimiento con datos: {"hookalias":"SAKFO9999","status":"confirmed","response_code":"00","response_description":"Pago exitoso","amount":438900,"currency":"GS","installment_number":10,"product_description":"Coca Cola 1 Ltr.","date_time":"31\/10\/2019 13:59:39","ticket_number":**************,"authorization_code":"134243","commerce_name":"Supermercado Maravilla","branch_name":"Sucursal San Vicente","bin":"433234","merchant_code":"51111","payer_name":null,"payer_lastname":null,"card_last_numbers":1234,"account_type":"TC","created_at":"2020-05-20T20:25:00.000Z"}
[2025-06-26 17:52:19] HTTP 500 Error: Server error occurred
=== Nueva Sesión Iniciada: 2025-06-26 17:52:32 ===
[2025-06-26 17:52:32] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_PUT_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-26 17:52:39 ===
[2025-06-26 17:52:39] Fallo el procedimiento - Data: "SQLSTATE: 42000, Code: 8144, Message: [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]La funci\u00f3n o el procedimiento SP_PUT_DATOS_TBANCARD tiene demasiados argumentos."
[2025-06-26 17:52:39] Error de base de datos: Fallo la consulta: SQLSTATE: 42000, Code: 8144, Message: [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]La función o el procedimiento SP_PUT_DATOS_TBANCARD tiene demasiados argumentos.
[2025-06-26 17:52:39] Error al ejecutar procedimiento con datos: {"hookalias":"SAKFO9989","status":"confirmed","response_code":"00","response_description":"Pago exitoso","amount":438900,"currency":"GS","installment_number":10,"product_description":"Coca Cola 1 Ltr.","date_time":"31\/10\/2019 13:59:39","ticket_number":**************,"authorization_code":"134243","commerce_name":"Supermercado Maravilla","branch_name":"Sucursal San Vicente","bin":"433234","merchant_code":"51111","payer_name":null,"payer_lastname":null,"card_last_numbers":1234,"account_type":"TC","created_at":"2020-05-20T20:25:00.000Z"}
[2025-06-26 17:52:39] HTTP 500 Error: Server error occurred
=== Nueva Sesión Iniciada: 2025-06-26 18:45:42 ===
[2025-06-26 18:45:42] Fallo el procedimiento (dev) - Data: "SQLSTATE: 42000, Code: 50000, Message: [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]La fecha proporcionada no tiene un formato v\u00e1lido. Use yyyy-mm-dd hh:mi:ss"
[2025-06-26 18:45:42] Error al ejecutar el procedimiento SP_PUT_ALIAS_TBANCARD - Data: {"hookalias":"SAGXK92147","amount":278600,"description":"Compra en Tienda de Nike SOL","created_at":"26/06/2025 15:45:41"}
=== Nueva Sesión Iniciada: 2025-06-26 18:45:43 ===
[2025-06-26 18:45:43] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-26 18:45:48 ===
[2025-06-26 18:45:48] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-26 18:45:53 ===
[2025-06-26 18:45:53] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-26 18:45:58 ===
[2025-06-26 18:45:58] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-26 18:46:03 ===
[2025-06-26 18:46:03] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-26 18:46:08 ===
[2025-06-26 18:46:08] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-26 18:46:13 ===
[2025-06-26 18:46:13] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-26 18:46:18 ===
[2025-06-26 18:46:18] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-26 18:46:23 ===
[2025-06-26 18:46:23] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-26 18:46:28 ===
[2025-06-26 18:46:28] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-26 18:46:33 ===
[2025-06-26 18:46:33] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-26 18:46:38 ===
[2025-06-26 18:46:38] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-26 18:46:43 ===
[2025-06-26 18:46:43] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-26 18:46:48 ===
[2025-06-26 18:46:48] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-26 18:46:53 ===
[2025-06-26 18:46:53] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-26 18:46:58 ===
[2025-06-26 18:46:58] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-26 18:47:03 ===
[2025-06-26 18:47:03] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-26 18:47:08 ===
[2025-06-26 18:47:08] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-26 18:47:13 ===
[2025-06-26 18:47:13] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-26 18:47:18 ===
[2025-06-26 18:47:18] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-26 18:47:23 ===
[2025-06-26 18:47:23] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-26 18:47:28 ===
[2025-06-26 18:47:28] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-26 18:47:33 ===
[2025-06-26 18:47:33] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-26 18:47:38 ===
[2025-06-26 18:47:38] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-26 18:48:51 ===
[2025-06-26 18:48:51] Fallo el procedimiento (dev) - Data: "SQLSTATE: 42000, Code: 50000, Message: [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]La fecha proporcionada no tiene un formato v\u00e1lido. Use yyyy-mm-dd hh:mi:ss"
[2025-06-26 18:48:51] Error al ejecutar el procedimiento SP_PUT_ALIAS_TBANCARD - Data: {"hookalias":"SCNEY98132","amount":278600,"description":"Compra en Tienda de Nike SOL","created_at":"26/06/2025 15:48:50"}
=== Nueva Sesión Iniciada: 2025-06-26 18:48:51 ===
[2025-06-26 18:48:51] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-26 18:48:56 ===
[2025-06-26 18:48:56] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-26 18:49:01 ===
[2025-06-26 18:49:01] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-26 18:49:06 ===
[2025-06-26 18:49:06] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-26 18:49:11 ===
[2025-06-26 18:49:11] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-26 18:49:14 ===
[2025-06-26 18:49:14] Fallo el procedimiento (dev) - Data: "SQLSTATE: 42000, Code: 50000, Message: [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]La fecha proporcionada no tiene un formato v\u00e1lido. Use yyyy-mm-dd hh:mi:ss"
[2025-06-26 18:49:14] Error al ejecutar el procedimiento SP_PUT_ALIAS_TBANCARD - Data: {"hookalias":"SAKFO9989","amount":"438900","description":"Prueba Guardar alias","created_at":"25/06/2025 12:00:00"}
=== Nueva Sesión Iniciada: 2025-06-26 18:49:16 ===
[2025-06-26 18:49:16] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-26 18:49:21 ===
[2025-06-26 18:49:21] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-26 18:49:26 ===
[2025-06-26 18:49:26] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-26 18:49:31 ===
[2025-06-26 18:49:31] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-26 18:49:36 ===
[2025-06-26 18:49:36] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-26 18:49:41 ===
[2025-06-26 18:49:41] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-26 18:49:46 ===
[2025-06-26 18:49:46] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-26 18:49:51 ===
[2025-06-26 18:49:51] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-26 18:49:56 ===
[2025-06-26 18:49:56] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-26 18:50:01 ===
[2025-06-26 18:50:01] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-26 18:50:06 ===
[2025-06-26 18:50:06] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-26 18:50:11 ===
[2025-06-26 18:50:11] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-26 18:50:16 ===
[2025-06-26 18:50:16] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-26 18:50:21 ===
[2025-06-26 18:50:21] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-26 18:50:26 ===
[2025-06-26 18:50:26] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-26 18:50:31 ===
[2025-06-26 18:50:31] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-26 18:50:36 ===
[2025-06-26 18:50:36] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-26 18:50:41 ===
[2025-06-26 18:50:41] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-26 18:50:46 ===
[2025-06-26 18:50:46] Procedimiento ejecutado exitosamente (dev) - Data: {"procedure":"SP_GET_ALIAS_TBANCARD","server":"***************","database":"rayco","results_count":0}
=== Nueva Sesión Iniciada: 2025-06-26 18:51:00 ===
[2025-06-26 18:51:00] Fallo el procedimiento (dev) - Data: "SQLSTATE: 42000, Code: 50000, Message: [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]La fecha proporcionada no tiene un formato v\u00e1lido. Use yyyy-mm-dd hh:mi:ss"
[2025-06-26 18:51:00] Error al ejecutar el procedimiento SP_PUT_ALIAS_TBANCARD - Data: {"hookalias":"SAKFO9989","amount":"438900","description":"Prueba Guardar alias","created_at":"25/06/2025 12:00:00"}
=== Nueva Sesión Iniciada: 2025-07-10 03:11:01 ===
=== Nueva Sesión Iniciada: 2025-07-10 03:14:47 ===
=== Nueva Sesión Iniciada: 2025-07-10 03:18:01 ===
=== Nueva Sesión Iniciada: 2025-07-10 03:18:03 ===
=== Nueva Sesión Iniciada: 2025-07-10 03:19:29 ===
=== Nueva Sesión Iniciada: 2025-07-10 03:19:32 ===
=== Nueva Sesión Iniciada: 2025-07-10 04:22:20 ===
=== Nueva Sesión Iniciada: 2025-07-10 04:22:26 ===
=== Nueva Sesión Iniciada: 2025-07-10 04:24:39 ===
=== Nueva Sesión Iniciada: 2025-07-10 04:26:37 ===
=== Nueva Sesión Iniciada: 2025-07-10 04:31:10 ===
=== Nueva Sesión Iniciada: 2025-07-10 04:31:15 ===
=== Nueva Sesión Iniciada: 2025-07-10 04:31:21 ===
=== Nueva Sesión Iniciada: 2025-07-10 04:35:10 ===
=== Nueva Sesión Iniciada: 2025-07-10 04:38:07 ===
=== Nueva Sesión Iniciada: 2025-07-10 04:41:09 ===
=== Nueva Sesión Iniciada: 2025-07-10 04:41:25 ===
=== Nueva Sesión Iniciada: 2025-07-10 04:42:46 ===
=== Nueva Sesión Iniciada: 2025-07-10 04:42:52 ===
=== Nueva Sesión Iniciada: 2025-07-10 04:44:41 ===
=== Nueva Sesión Iniciada: 2025-07-11 12:25:05 ===
=== Nueva Sesión Iniciada: 2025-07-11 14:40:56 ===
=== Nueva Sesión Iniciada: 2025-07-11 14:40:59 ===
=== Nueva Sesión Iniciada: 2025-07-14 22:15:23 ===
=== Nueva Sesión Iniciada: 2025-07-14 22:15:26 ===
=== Nueva Sesión Iniciada: 2025-07-14 22:15:33 ===
=== Nueva Sesión Iniciada: 2025-07-14 22:15:45 ===
=== Nueva Sesión Iniciada: 2025-07-14 22:15:56 ===
=== Nueva Sesión Iniciada: 2025-07-14 22:31:43 ===
=== Nueva Sesión Iniciada: 2025-07-14 22:31:47 ===
=== Nueva Sesión Iniciada: 2025-07-14 22:32:49 ===
=== Nueva Sesión Iniciada: 2025-07-14 22:33:32 ===
=== Nueva Sesión Iniciada: 2025-07-21 03:19:10 ===
=== Nueva Sesión Iniciada: 2025-07-21 03:32:54 ===
=== Nueva Sesión Iniciada: 2025-07-21 03:36:03 ===
=== Nueva Sesión Iniciada: 2025-07-21 03:36:28 ===
=== Nueva Sesión Iniciada: 2025-07-21 03:37:12 ===
=== Nueva Sesión Iniciada: 2025-07-21 03:42:19 ===
=== Nueva Sesión Iniciada: 2025-07-21 03:42:27 ===
=== Nueva Sesión Iniciada: 2025-07-21 03:45:18 ===
=== Nueva Sesión Iniciada: 2025-07-21 03:47:11 ===
=== Nueva Sesión Iniciada: 2025-07-21 15:01:12 ===
=== Nueva Sesión Iniciada: 2025-07-28 14:46:14 ===
=== Nueva Sesión Iniciada: 2025-07-28 14:47:58 ===
=== Nueva Sesión Iniciada: 2025-07-28 14:49:32 ===
=== Nueva Sesión Iniciada: 2025-07-30 14:59:44 ===
=== Nueva Sesión Iniciada: 2025-07-30 15:18:32 ===
=== Nueva Sesión Iniciada: 2025-07-30 15:18:32 ===
=== Nueva Sesión Iniciada: 2025-07-30 15:18:32 ===
=== Nueva Sesión Iniciada: 2025-07-30 15:51:53 ===
=== Nueva Sesión Iniciada: 2025-07-30 15:59:30 ===
=== Nueva Sesión Iniciada: 2025-07-30 19:20:22 ===
=== Nueva Sesión Iniciada: 2025-07-30 19:31:35 ===
=== Nueva Sesión Iniciada: 2025-07-30 19:40:09 ===
=== Nueva Sesión Iniciada: 2025-07-30 19:41:18 ===
=== Nueva Sesión Iniciada: 2025-07-30 19:41:18 ===
=== Nueva Sesión Iniciada: 2025-07-30 20:02:45 ===
=== Nueva Sesión Iniciada: 2025-07-30 20:03:09 ===
=== Nueva Sesión Iniciada: 2025-07-30 20:04:51 ===
=== Nueva Sesión Iniciada: 2025-07-30 20:04:51 ===
